import { bool, number, oneOfType, shape, string } from 'prop-types'
import { useMemo } from 'react'
import { FormattedMessage } from 'react-intl'

import { Conditional, ShowIf } from 'components/common/Conditional'
import { Icon } from 'components/common/Icon'
import { SkeletonLoader } from 'components/common/SkeletonLoader'
import { useBuildingPricing } from 'hooks/useBuildingPricing'
import { useTranslation } from 'hooks/useTranslation'

export const Prices = ({ className = 'px-4', filter }) => {
  const {
    pricingByCapacity: pricingByCapacityRaw,
    pricingBySoapDispenser: pricingBySoapDispenserRaw,
    capacitiesDistinction,
    soapDispensersDistinction,
    available,
    isFetching,
  } = useBuildingPricing()

  const { pricingByCapacity, pricingBySoapDispenser } = useMemo(
    () => ({
      pricingByCapacity: pricingByCapacityRaw.filter((pricing) =>
        filter?.capacity ? pricing.capacity === +filter.capacity : true
      ),
      pricingBySoapDispenser: pricingBySoapDispenserRaw.filter((pricing) =>
        filter?.soapDispenser !== undefined
          ? pricing.hasSoapDispenser === filter.soapDispenser
          : true
      ),
    }),
    [filter, pricingByCapacityRaw, pricingBySoapDispenserRaw]
  )

  if (isFetching) return <Skeleton />
  if (!available) return <></>

  return (
    <div className={`flex justify-around flex-wrap ${className}`}>
      <Conditional>
        <ShowIf condition={capacitiesDistinction}>
          {pricingByCapacity.map((pricing) => (
            <PriceByKg
              key={pricing.name}
              capacity={pricing.capacity}
              price={pricing.priceCustomer}
            />
          ))}
        </ShowIf>
        <ShowIf condition={soapDispensersDistinction}>
          {pricingBySoapDispenser.map((pricing) => (
            <PriceBySoapDispenser
              key={pricing.name}
              hasSoapDispenser={pricing.hasSoapDispenser}
              price={pricing.priceCustomer}
            />
          ))}
        </ShowIf>
      </Conditional>
    </div>
  )
}

Prices.propTypes = {
  className: string,
  filter: shape({
    capacity: oneOfType([number, string]),
    soapDispenser: bool,
  }),
}

const PriceByKg = ({ capacity = 0, price = 0 }) => {
  const { t } = useTranslation()

  return (
    <div className='flex flex-col items-center'>
      <h3 className='font-light mb-2'>
        <FormattedMessage
          id='global.prices.capacity'
          values={{
            s: (chunks) => <span className='font-medium'>{chunks}</span>,
            capacity: capacity,
          }}
        />
      </h3>
      <p className='text-skyblue font-medium uppercase lm__title--page'>
        {t('global.currency.full')}
        {price}
      </p>
    </div>
  )
}

const PriceBySoapDispenser = ({ hasSoapDispenser = false, price = 0 }) => {
  const { t } = useTranslation()

  return (
    <div className='flex flex-col items-center'>
      <h3 className='font-light order-2'>
        {t('global.prices.soapDispenser', { hasSoapDispenser })}
        {hasSoapDispenser ? (
          <Icon
            kind='soapBrands'
            className='w-32 mt-2'
            color='blue'
            srText={t('global.prices.soapBrands')}
          />
        ) : null}
      </h3>
      <p className='text-skyblue font-medium uppercase lm__title--page order-1 mb-2'>
        {t('global.currency.full')}
        {price}
      </p>
    </div>
  )
}

const Skeleton = () => (
  <div className='flex flex-col items-center'>
    <SkeletonLoader className='w-[100px] h-[100px]' />
  </div>
)
