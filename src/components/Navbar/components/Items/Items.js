import { lazy, useState } from 'react'

import { useTranslation } from 'hooks/useTranslation'
import { useExperience } from 'lib/experience'

const LaundromatItems = lazy(() =>
  import(/* webpackChunkName: "lm-exp" */ './experiences/LaundromatItems')
)
const DefaultItems = lazy(() => import('./experiences/DefaultItems'))

export const Items = () => {
  const { isNonLoggedInLaundromat, isLoggedInLaundromat } = useExperience()
  const [authError, setAuthError] = useState(false)

  const handleAuthError = () => setAuthError(true)

  if (authError) return <AuthError />

  if (isNonLoggedInLaundromat || isLoggedInLaundromat) {
    return <LaundromatItems onAuthError={handleAuthError} />
  }

  return <DefaultItems onAuthError={handleAuthError} />
}

const AuthError = () => {
  const { t } = useTranslation()

  return (
    <div className='bg-cornflower opacity-70 rounded-3xl mb-20 min-h-[50%] flex items-center mx-12 p-12'>
      <p className='lm__font--60 font-light text-blue'>
        {t('home.menu.error.auth')}
      </p>
    </div>
  )
}
