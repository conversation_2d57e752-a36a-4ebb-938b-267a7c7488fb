import { useMemo } from 'react'
import { orderBy } from 'lodash'

import { Logo } from 'components/common/Logo'
import { Spinner } from 'components/common/Spinner'
import { useGetLaundromatsQuery } from 'services/building'

export const Stores = () => {
  const { data, isFetching, isError } = useGetLaundromatsQuery()
  const laundromats = useMemo(() => {
    if (!data) return []

    return orderBy(
      data,
      [
        // First criteria: prioritize items starting with "Montevideo" (false = first)
        (item) =>
          !item.googleMaps?.name?.toLowerCase().startsWith('montevideo'),
        // Second criteria: alphabetical order by name
        (item) => item.googleMaps?.name?.toLowerCase(),
      ],
      ['asc', 'asc']
    )
  }, [data])

  if (isFetching || isError) return <Spinner />

  return (
    <ul>
      {laundromats?.map(({ googleMaps = {} }, index) => (
        <Store key={index} {...googleMaps} />
      ))}
    </ul>
  )
}

const Store = ({ name, link = '#' }) => {
  if (!name) return null

  return (
    <li className='flex items-center mb-12'>
      <Logo className='w-12 h-12 mr-8' />
      <span className='underline'>
        <a href={link} className='capitalize'>
          {name}
        </a>
        {'.'}
      </span>
    </li>
  )
}
