import { Redirect } from 'react-router-dom'

import { Conditional, ShowIf } from 'components/common/Conditional'
import { ACTIVATION_FEATURE_ENABLED } from 'constants/configurations'
import { useBuilding } from 'hooks/useBuilding'
import { hashes, paths } from 'router/paths'

import { Done } from './components/states/Done'
import { Error } from './components/states/Error'
import { Main } from './components/states/Main'
import { Processing } from './components/states/Processing'
import { Success } from './components/states/Success'
import {
  PHASE,
  StateMachineProvider,
  useStateMachine,
} from './contexts/StateMachineContext'

export const Activation = () => {
  const { slug } = useBuilding()

  if (!ACTIVATION_FEATURE_ENABLED) {
    return <Redirect to={paths.indexWithBuilding(slug, hashes.map)} />
  }

  return (
    <StateMachineProvider>
      <Content />
    </StateMachineProvider>
  )
}

const Content = () => {
  const { phase } = useStateMachine()

  return (
    <Conditional>
      <ShowIf condition={phase === PHASE.main}>
        <Main />
      </ShowIf>
      <ShowIf condition={phase === PHASE.processing}>
        <Processing />
      </ShowIf>
      <ShowIf condition={phase === PHASE.success}>
        <Success />
      </ShowIf>
      <ShowIf condition={phase === PHASE.done}>
        <Done />
      </ShowIf>
      <ShowIf condition={phase === PHASE.error}>
        <Error />
      </ShowIf>
    </Conditional>
  )
}
