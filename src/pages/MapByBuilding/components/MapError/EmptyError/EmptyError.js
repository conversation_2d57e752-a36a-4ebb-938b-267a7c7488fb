import { ButtonIcon } from 'components/common/Button'
import { Warning } from 'components/common/Warning'
import { useTranslation } from 'hooks/useTranslation'

import { useStateMap } from '../../../hooks/useStateMap'

export const EmptyError = ({ backButton = false }) => {
  const { t } = useTranslation()
  const { reset } = useStateMap()

  const goHomepage = () => {
    reset()
  }

  return (
    <div className='flex flex-col items-center'>
      {backButton ? (
        <div className='absolute top-8 left-8 flex items-center'>
          <ButtonIcon
            icon='back'
            onClick={goHomepage}
            color='blue'
            kind='tertiary'
          />
          <span className='lm__font--40 ml-2'>{t('global.cta.back')}</span>
        </div>
      ) : null}

      <Warning className='mb-14 text-blue' />

      <h2 className='lm__title--sub text-center w-4/5 mb-14'>
        {t('mapByBuilding.error.empty.title')}
      </h2>
    </div>
  )
}
