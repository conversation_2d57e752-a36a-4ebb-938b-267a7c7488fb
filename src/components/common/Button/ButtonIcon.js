import cn from 'classnames'
import { oneOf } from 'prop-types'

import useTranslation from 'hooks/useTranslation'

import { Icon } from '../Icon'
import { Spinner } from '../Spinner'
import { basePropTypes, classes, icons, kinds, types } from './base'

export const ButtonIcon = ({
  children,
  className = '',
  icon = icons.NEXT,
  kind = kinds.PRIMARY,
  type = types.BUTTON,
  loading,
  srText,
  color = 'white',
  ...props
}) => {
  const { t } = useTranslation()
  const buttonClasses = cn(classes.BASE, 'px-4', className, {
    [classes.PRIMARY]: kind === kinds.PRIMARY,
    [classes.SECONDARY]: kind === kinds.SECONDARY,
    [classes.TERTIARY]: kind === kinds.TERTIARY,
  })
  const iconClasses = cn('w-6 h-6', {
    '-rotate-180': icon === icons.BACK,
  })
  const Element = type === types.ANCHOR ? 'a' : 'button'

  return (
    <Element className={buttonClasses} type={type} {...props}>
      {loading ? (
        <Spinner size={6} />
      ) : (
        <Icon
          kind='chevron'
          srText={srText || t(`global.cta.${icon}`)}
          className={iconClasses}
          color={color}
        />
      )}
      {children}
    </Element>
  )
}

ButtonIcon.propTypes = {
  ...basePropTypes,
  icon: oneOf(Object.values(icons)),
}
