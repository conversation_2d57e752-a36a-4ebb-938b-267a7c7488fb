import { useMemo } from 'react'

import { uniqWith } from 'lodash'

import { useGetPricingQuery } from 'services/building'
import { useBuilding } from 'hooks/useBuilding'

export const useBuildingPricing = () => {
  const { slug } = useBuilding()
  const { data: pricing, ...rest } = useGetPricingQuery(
    { slug },
    {
      skip: !slug,
    }
  )

  const pricingByCapacity = useMemo(() => {
    const unique = uniqWith(pricing, (a, b) => a.capacity === b.capacity)

    return unique?.sort((a, b) => a.capacity - b.capacity)
  }, [pricing])

  const pricingBySoapDispenser = useMemo(() => {
    const uniqueRates = uniqWith(
      pricing,
      (a, b) => a.hasSoapDispenser === b.hasSoapDispenser
    )

    return uniqueRates?.sort((a, b) => a.hasSoapDispenser - b.hasSoapDispenser)
  }, [pricing])

  return {
    pricingByCapacity,
    pricingBySoapDispenser,
    available: !!(pricingByCapacity?.length && pricingBySoapDispenser?.length),
    capacitiesDistinction:
      pricingByCapacity?.length >= pricingBySoapDispenser?.length,
    soapDispensersDistinction:
      pricingBySoapDispenser?.length > pricingByCapacity?.length,
    ...rest,
  }
}
