import useTranslation from 'hooks/useTranslation'

export const Cost = ({ rate, className = '' }) => {
  const { t } = useTranslation()

  return (
    <div className={`flex flex-col justify-center items-center ${className}`}>
      <h2 className='lm__title--sub font-light text-center mb-4'>
        {t('cardDetail.cost.title')}
      </h2>
      <span className='text-skyblue lm__font--80 font-extrabold bg-cornflower rounded-2xl py-4 px-8 text-center mb-8'>
        {t('cardDetail.cost.value', { price: rate?.priceCustomer || '--' })}
      </span>
    </div>
  )
}
