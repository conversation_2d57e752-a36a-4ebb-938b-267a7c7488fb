import cn from 'classnames'
import { oneOf, string } from 'prop-types'

import { CLASSES, COLOR } from 'styles/constants'

import { icons } from './components'

export const Icon = ({
  kind,
  color = COLOR.black,
  className = '',
  srText,
  ...props
}) => {
  const Component = icons[kind]

  if (!Component) {
    console.error(`The icon ‘${kind}‘ does not exist.`)
    return null
  }

  const classes = cn(className, CLASSES.fill(color), CLASSES.stroke(color))

  return (
    <>
      {srText ? <span className='sr-only'>{srText}</span> : null}
      <Component aria-hidden='true' className={classes} {...props} />
    </>
  )
}

Icon.propTypes = {
  kind: oneOf(Object.keys(icons)),
  color: string,
  srText: string,
}

export default Icon
