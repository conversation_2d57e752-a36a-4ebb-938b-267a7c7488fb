import { useState } from 'react'

import { Conditional, ShowIf } from 'components/common/Conditional'
import { Logo } from 'components/common/Logo'
import { useTranslation } from 'hooks/useTranslation'

import { ActivationMessage } from './components/ActivationMessage'
import { Form } from './components/Form'

const STATES = {
  REGISTERING: 'registering',
  SIGNED_UP: 'signed-up',
}
export const Signup = () => {
  const { t } = useTranslation()
  const [state, setState] = useState(STATES.REGISTERING)

  const handleSignUp = () => setState(STATES.SIGNED_UP)

  return (
    <div className='relative flex flex-col items-center h-screen'>
      <h1 className='my-10 lm__font--60 uppercase text-skyblue text-center flex flex-row items-center'>
        <Logo className='w-12 h-12 mr-2' />

        {t('singup.title')}
      </h1>

      <Conditional>
        <ShowIf isDefault>
          <Form onSuccess={handleSignUp} />
        </ShowIf>
        <ShowIf condition={state === STATES.SIGNED_UP}>
          <ActivationMessage />
        </ShowIf>
      </Conditional>
    </div>
  )
}
