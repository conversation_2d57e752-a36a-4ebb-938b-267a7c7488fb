import { Button } from 'components/common/Button'
import { Layout } from 'components/common/Layout'
import { SectionTitle } from 'components/common/SectionTitle'
import { Wave } from 'components/common/Wave'
import { CHAT_BOT } from 'constants/business'
import useTranslation from 'hooks/useTranslation'
import { hashes } from 'router/paths'

export const VirtualAssistant = () => {
  const { t } = useTranslation()

  return (
    <Layout.Section
      id={hashes.virtualAssistant}
      className='relative flex flex-col items-center justify-around h-screen'
    >
      <SectionTitle intlId='virtualAssistant.title' />
      <Button type='a' href={CHAT_BOT.link}>
        {t('virtualAssistant.cta')}
      </Button>

      <Wave direction='up' peakCount={2} height={10} />
    </Layout.Section>
  )
}
