import cn from 'classnames'
import { oneOf } from 'prop-types'

import { CLASSES, COLOR } from 'styles/constants'

import { variants } from './variants'

export const Wave = ({
  direction = '',
  peakCount = 0,
  className = '',
  color = COLOR.cornflower,
  height = 0,
}) => {
  if (!direction || !peakCount) return null

  const name = direction + peakCount.toString()
  const Variant = variants[name]

  if (!Variant) {
    console.error(`The Wave ‘${name}‘ does not exist.`)
    return null
  }

  const containerClasses = cn(className, 'w-screen -z-50 absolute', {
    'top-0': direction === 'down',
    'bottom-0': direction === 'up',
  })

  const waveClasses = cn(CLASSES.fill(color), {
    '-translate-y-px': direction === 'down',
    'translate-y-px': direction === 'up',
  })

  const fillClasses = cn(CLASSES.bg(color), {
    'h-[5dvh]': height === 5,
    'h-[10dvh]': height === 10,
    'h-[15dvh]': height === 15,
    'h-[20dvh]': height === 20,
  })

  return (
    <div className={containerClasses} aria-hidden='true'>
      {direction === 'down' && <div className={fillClasses} />}
      <Variant className={waveClasses} />
      {direction === 'up' && <div className={fillClasses} />}
    </div>
  )
}

Wave.propTypes = {
  direction: oneOf(['down', 'up']).isRequired,
  peakCount: oneOf([2, 3]).isRequired,
  color: oneOf(Object.values(COLOR)),
  height: oneOf([0, 5, 10, 15, 20]),
}
