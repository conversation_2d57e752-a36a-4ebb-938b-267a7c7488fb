# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
coverage_jest
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# node-waf configuration
.lock-wscript

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/
build/Release

# Dependency directory
# https://www.npmjs.org/doc/misc/npm-faq.html#should-i-check-my-node_modules-folder-into-git
node_modules

#dist folder
dist

#Webstorm metadata
.idea

#VSCode metadata
.vscode

# Mac files
.DS_Store

# Environment variables
.env
.env.dev
.env.staging
.env.prod
.env.test_e2e
.env.local
.env.development.local
.env.test.local
.env.production.local

# Server build
server/build

# Cypress
cypress.env.json
cypress/screenshots
cypress/snapshots
cypress/videos
