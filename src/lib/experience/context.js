import { createContext, useCallback, useEffect, useState } from 'react'

import { LAUNDROMAT_ONLY_EXPERIENCE_FEATURES } from 'constants/configurations'
import { BUILDING } from 'constants/models/building'
import { useAuth } from 'hooks/useAuth'
import { useBuilding } from 'hooks/useBuilding'

export const ExperienceContext = createContext(null)

export const EXPERIENCE = {
  nonLoggedIn: {
    laundromat: 'NLLM',
    building: 'NLBD',
    unknown: 'NLUNK',
  },
  loggedIn: {
    laundromat: 'LLM',
    building: 'LBD',
    unknown: 'LUNK',
  },
}

export const ExperienceProvider = ({ children }) => {
  const [experience, setExperience] = useState()
  const { authenticated } = useAuth()
  const { building } = useBuilding()

  const evaluate = useCallback(() => {
    const userCase = authenticated
      ? EXPERIENCE.loggedIn
      : EXPERIENCE.nonLoggedIn

    if (building?.type === BUILDING.TYPE.LAUNDROMAT) {
      setExperience(userCase.laundromat)
    } else if (
      (building?.type === BUILDING.TYPE.BUILDING ||
        building?.type === BUILDING.TYPE.COLIVING) &&
      !LAUNDROMAT_ONLY_EXPERIENCE_FEATURES
    ) {
      setExperience(userCase.building)
    } else {
      setExperience(userCase.unknown)
    }
  }, [authenticated, building])

  useEffect(() => {
    evaluate()
  }, [evaluate, authenticated, building])

  return (
    <ExperienceContext.Provider
      value={{
        experience,
        isActivationEnabled: !!building?.isRemoteActivationEnabled,
      }}
    >
      {children}
    </ExperienceContext.Provider>
  )
}
