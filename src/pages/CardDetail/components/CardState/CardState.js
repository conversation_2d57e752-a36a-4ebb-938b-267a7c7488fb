import { CARD } from 'constants/models/card'
import useTranslation from 'hooks/useTranslation'

const map = {
  [CARD.STATE.NEW]: 'active',
  [CARD.STATE.ACTIVE]: 'active',
  [CARD.STATE.INACTIVE]: 'inactive',
  [CARD.STATE.SUSPENDED]: 'suspended',
  [CARD.STATE.LOST]: 'lost',
  [CARD.STATE.DAMAGED]: 'damaged',
  [CARD.STATE.PRE_BLOCKED]: 'preBlocked',
}

export const CardState = ({ state }) => {
  const { t } = useTranslation()

  if (!state) return ''

  return t(`global.card.state.${map[state]}`)
}
