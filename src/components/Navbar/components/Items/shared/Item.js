import { Link } from 'react-router-dom'

import useAuth from 'hooks/useAuth'

export const Item = ({ path, name, onClick }) => (
  <li className='lm__font--60 uppercase font-light basis-1/6'>
    <Link to={path} onClick={onClick}>
      {name}
    </Link>
  </li>
)

export const PrivateItem = ({ onAuthError, ...props }) => {
  const { authenticated } = useAuth()

  if (!authenticated) {
    return (
      <li className='basis-1/6'>
        <button
          className='lm__font--60 uppercase font-light'
          onClick={onAuthError}
        >
          {props.name}
        </button>
      </li>
    )
  }

  return <Item {...props} />
}
