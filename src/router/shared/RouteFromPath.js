import { Redirect, Route, useLocation } from 'react-router-dom'

import { Layout } from 'components/common/Layout'
import { EXPERIENCE, useExperience } from 'lib/experience'

import { paths } from '../paths'

export const RouteFromPath = ({
  component: Component,
  redirectTo,
  experiences = [],
  ...route
}) => {
  const location = useLocation()
  const { experience } = useExperience()
  const routeAllowed =
    experiences.length === 0 || experiences.includes(experience)
  const routeRequiresLogin = experiences.some(
    (exp) =>
      exp === EXPERIENCE.loggedIn.building ||
      exp === EXPERIENCE.loggedIn.laundromat ||
      exp === EXPERIENCE.loggedIn.unknown
  )

  if (redirectTo) {
    return (
      <Redirect
        to={{
          pathname: redirectTo,
          state: { from: location.pathname },
        }}
      />
    )
  }

  if (!routeAllowed && routeRequiresLogin) {
    // If user is not in allowed experience and route requires being logged in,
    // redirect to login adding the page that comes from to future redirection
    // Example:
    // 1. non-logged in user trying to access /map/:slug,
    // 2. they go first to /login
    // 3. after successfully login, they are redirected to /map/:slug
    return (
      <Redirect
        to={{ pathname: paths.login, state: { from: location.pathname } }}
      />
    )
  } else if (!routeAllowed) {
    // If user is not in allowed experience,
    // redirect to the page that comes from the state or homepage
    // Example:
    // 1. logged in user trying to access /login
    // 3. they are redirected to /
    return <Redirect to={{ pathname: location.state?.from || paths.index }} />
  }

  return (
    <Route {...route}>
      <Layout.Page>
        <Component />
      </Layout.Page>
    </Route>
  )
}
