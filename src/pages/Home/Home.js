import { lazy } from 'react'

import { useExperience } from 'lib/experience'

const NonLoggedInLaundromatHome = lazy(() =>
  import(
    /* webpackChunkName: "nllm-exp" */ './experiences/NonLoggedInLaundromatHome'
  )
)

const LoggedInLaundromatHome = lazy(() =>
  import(
    /* webpackChunkName: "llm-exp" */ './experiences/LoggedInLaundromatHome'
  )
)

const NonLoggedInBuildingHome = lazy(() =>
  import(
    /* webpackChunkName: "nlbd-exp" */ './experiences/NonLoggedInBuildingHome'
  )
)

const LoggedInBuildingHome = lazy(() =>
  import(/* webpackChunkName: "lbd-exp" */ './experiences/LoggedInBuildingHome')
)

const NonLoggedInUnknown = lazy(() =>
  import(
    /* webpackChunkName: "non-logged-in-exp" */ './experiences/NonLoggedInUnknown'
  )
)

const DefaultHome = lazy(() => import('./experiences/DefaultHome'))

export const Home = () => {
  const {
    isNonLoggedInLaundromat,
    isLoggedInLaundromat,
    isNonLoggedInBuilding,
    isLoggedInBuilding,
    isNonLoggedInUnknown,
  } = useExperience()

  if (isNonLoggedInLaundromat) return <NonLoggedInLaundromatHome />
  if (isLoggedInLaundromat) return <LoggedInLaundromatHome />
  if (isNonLoggedInBuilding) return <NonLoggedInBuildingHome />
  if (isLoggedInBuilding) return <LoggedInBuildingHome />
  if (isNonLoggedInUnknown) return <NonLoggedInUnknown />
  return <DefaultHome />
}
