export const Start = (props) => (
  <svg viewBox='0 0 171 171' xmlns='http://www.w3.org/2000/svg' {...props}>
    <path d='M47.432 84.925C45.076 84.119 43.371 83.158 42.317 82.042C41.2837 80.9053 40.767 79.5413 40.767 77.95C40.767 76.1313 41.3457 74.695 42.503 73.641C43.6603 72.587 45.2517 72.06 47.277 72.06C49.1783 72.06 50.8523 72.2873 52.299 72.742C52.733 72.8867 53.0843 73.1657 53.353 73.579C53.6423 73.9717 53.787 74.416 53.787 74.912C53.787 75.2633 53.632 75.532 53.322 75.718C53.012 75.904 52.6917 75.9247 52.361 75.78C50.7903 75.1187 49.199 74.788 47.587 74.788C46.471 74.788 45.6133 75.067 45.014 75.625C44.4147 76.183 44.115 76.958 44.115 77.95C44.115 78.756 44.394 79.4897 44.952 80.151C45.51 80.7917 46.3367 81.298 47.432 81.67C50.16 82.5793 52.051 83.592 53.105 84.708C54.1797 85.824 54.717 87.2397 54.717 88.955C54.717 91.0423 54.0763 92.6233 52.795 93.698C51.5137 94.7727 49.6227 95.31 47.122 95.31C45.4893 95.31 44.0117 95.062 42.689 94.566C41.7177 94.1733 41.232 93.4087 41.232 92.272C41.232 91.9207 41.387 91.6623 41.697 91.497C42.007 91.3317 42.317 91.3317 42.627 91.497C43.9083 92.179 45.3033 92.52 46.812 92.52C49.8087 92.52 51.307 91.3317 51.307 88.955C51.307 88.0457 51.0073 87.2707 50.408 86.63C49.8293 85.9893 48.8373 85.421 47.432 84.925ZM59.0856 75.222C58.6929 75.222 58.3519 75.0877 58.0626 74.819C57.7939 74.5297 57.6596 74.1887 57.6596 73.796C57.6596 73.4033 57.7939 73.0727 58.0626 72.804C58.3519 72.5147 58.6929 72.37 59.0856 72.37H72.3536C72.7462 72.37 73.0769 72.5147 73.3456 72.804C73.6349 73.0727 73.7796 73.4033 73.7796 73.796C73.7796 74.1887 73.6349 74.5297 73.3456 74.819C73.0769 75.0877 72.7462 75.222 72.3536 75.222H67.7036C67.5176 75.222 67.4246 75.315 67.4246 75.501V93.295C67.4246 93.7703 67.2592 94.1733 66.9286 94.504C66.5979 94.8347 66.1949 95 65.7196 95C65.2442 95 64.8412 94.8347 64.5106 94.504C64.1799 94.1733 64.0146 93.7703 64.0146 93.295V75.501C64.0146 75.315 63.9216 75.222 63.7356 75.222H59.0856ZM82.1941 75.501L78.9391 86.072C78.9185 86.134 78.9288 86.196 78.9701 86.258C79.0115 86.2993 79.0631 86.32 79.1251 86.32H85.3251C85.3871 86.32 85.4388 86.2993 85.4801 86.258C85.5215 86.196 85.5318 86.134 85.5111 86.072L82.2561 75.501C82.2561 75.4803 82.2458 75.47 82.2251 75.47C82.2045 75.47 82.1941 75.4803 82.1941 75.501ZM74.4441 95C74.0101 95 73.6588 94.8243 73.3901 94.473C73.1421 94.1217 73.0905 93.7393 73.2351 93.326L79.8691 74.044C80.0345 73.548 80.3341 73.145 80.7681 72.835C81.2021 72.525 81.6775 72.37 82.1941 72.37H82.3801C82.9175 72.37 83.3928 72.525 83.8061 72.835C84.2401 73.145 84.5398 73.548 84.7051 74.044L91.3391 93.326C91.4838 93.7393 91.4218 94.1217 91.1531 94.473C90.9051 94.8243 90.5641 95 90.1301 95H90.0061C89.4895 95 89.0141 94.845 88.5801 94.535C88.1668 94.225 87.8775 93.822 87.7121 93.326L86.4721 89.234C86.4515 89.0687 86.3378 88.986 86.1311 88.986H78.3191C78.1331 88.986 78.0195 89.0687 77.9781 89.234L76.7381 93.326C76.5935 93.822 76.3041 94.225 75.8701 94.535C75.4568 94.845 74.9815 95 74.4441 95ZM97.9608 75.284V82.755C97.9608 82.941 98.0538 83.034 98.2398 83.034H99.8208C104.202 83.034 106.393 81.546 106.393 78.57C106.393 77.3093 105.917 76.369 104.967 75.749C104.037 75.1083 102.528 74.788 100.441 74.788C99.6555 74.788 98.9115 74.85 98.2088 74.974C98.0435 75.0153 97.9608 75.1187 97.9608 75.284ZM97.4648 94.504C97.1341 94.8347 96.7311 95 96.2558 95C95.7805 95 95.3775 94.8347 95.0468 94.504C94.7161 94.1733 94.5508 93.7703 94.5508 93.295V74.292C94.5508 73.7753 94.7161 73.331 95.0468 72.959C95.3775 72.5663 95.8011 72.3493 96.3178 72.308C97.8471 72.1427 99.3248 72.06 100.751 72.06C103.727 72.06 105.969 72.6077 107.478 73.703C108.986 74.7983 109.741 76.3173 109.741 78.26C109.741 79.8307 109.338 81.174 108.532 82.29C107.746 83.3853 106.661 84.15 105.277 84.584C105.256 84.584 105.246 84.5943 105.246 84.615C105.246 84.6563 105.256 84.677 105.277 84.677C106.165 85.2763 106.982 86.4957 107.726 88.335L109.71 93.357C109.875 93.7497 109.834 94.1217 109.586 94.473C109.338 94.8243 109.007 95 108.594 95C108.056 95 107.571 94.8553 107.137 94.566C106.703 94.256 106.393 93.853 106.207 93.357L104.254 88.335C103.82 87.219 103.334 86.506 102.797 86.196C102.28 85.8653 101.288 85.7 99.8208 85.7H98.2398C98.0538 85.7 97.9608 85.793 97.9608 85.979V93.295C97.9608 93.7703 97.7955 94.1733 97.4648 94.504ZM113.214 75.222C112.822 75.222 112.481 75.0877 112.191 74.819C111.923 74.5297 111.788 74.1887 111.788 73.796C111.788 73.4033 111.923 73.0727 112.191 72.804C112.481 72.5147 112.822 72.37 113.214 72.37H126.482C126.875 72.37 127.206 72.5147 127.474 72.804C127.764 73.0727 127.908 73.4033 127.908 73.796C127.908 74.1887 127.764 74.5297 127.474 74.819C127.206 75.0877 126.875 75.222 126.482 75.222H121.832C121.646 75.222 121.553 75.315 121.553 75.501V93.295C121.553 93.7703 121.388 94.1733 121.057 94.504C120.727 94.8347 120.324 95 119.848 95C119.373 95 118.97 94.8347 118.639 94.504C118.309 94.1733 118.143 93.7703 118.143 93.295V75.501C118.143 75.315 118.05 75.222 117.864 75.222H113.214Z' />
    <path d='M85.4 170.8C73.6207 170.8 62.6343 168.535 52.2141 164.118C41.7939 159.7 32.8462 153.584 25.1443 145.882C17.4425 138.18 11.3263 129.119 6.79576 118.812C2.26525 108.392 0 97.2926 0 85.4C0 73.6207 2.26525 62.6342 6.79576 52.2141C11.3263 41.7939 17.4425 32.7329 25.1443 25.031C32.8462 17.3292 41.9072 11.213 52.2141 6.68249C62.6343 2.26525 73.6207 0 85.4 0C97.1793 0 108.166 2.26525 118.586 6.68249C129.006 11.0997 138.067 17.2159 145.769 25.031C153.471 32.8462 159.587 41.9072 164.118 52.2141C168.535 62.6342 170.8 73.6207 170.8 85.4C170.8 97.2926 168.535 108.392 164.118 118.812C159.7 129.233 153.584 138.18 145.769 145.882C137.954 153.584 128.893 159.587 118.586 164.118C108.166 168.648 97.1793 170.8 85.4 170.8ZM85.4 156.529C105.221 156.529 121.984 149.62 135.802 135.802C149.62 121.984 156.529 105.221 156.529 85.4C156.529 65.5791 149.62 48.8162 135.802 34.9981C121.984 21.1801 105.221 14.2711 85.4 14.2711C65.5791 14.2711 48.8162 21.1801 34.9982 34.9981C21.1801 48.8162 14.2711 65.5791 14.2711 85.4C14.2711 105.221 21.1801 121.984 34.9982 135.802C48.8162 149.62 65.5791 156.529 85.4 156.529Z' />
  </svg>
)

Start.toString = () => 'start'
