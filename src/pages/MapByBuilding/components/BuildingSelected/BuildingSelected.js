import { Dropdown } from 'components/common/Dropdown'

import { useMapBuilding } from '../../hooks/useMapBuilding'

export const BuildingSelected = () => {
  const { building, isFetching } = useMapBuilding()

  const buildings = [
    {
      id: building?.slug,
      name: building?.name,
    },
  ]

  if (isFetching) return null

  return (
    <Dropdown
      className='w-4/5 mt-10 mb-2'
      data={buildings}
      selected={buildings?.[0]}
      deactivateOnSingleOption
    />
  )
}
