<!---
Add a PR name following the pattern:
    [sc-<card-number>] Card Name,
--->

## Ticket

<!---
Insert Shortcut card reference (this options should auto reference the card, if doesn't work, please, use the next alternative)

    sc-<card-number>

Or you can add the card name and link

    [card-name](card-link)

--->


## Changes
<!---
What and why you do
--->

*

-------

<!---
The changes were tested by reviewers on the Review App, and everything works as expected
--->

- [ ] Tested on the Review App by reviewers

## Preview
<!---
Add screenshots if necessary, making sure what you want to show is clear, use arrows or boxes to highlight it if it'd be necessary

You can use a table to handle the image's size

|     |     |     |     |
|:---:|:---:|:---:|:---:|
|     |     |     |     |

--->



## How it works
<!---
Add examples if it's necessary: endpoint request/response, how call a service, etc.

```http

POST /api/v1/login
Body:
{
    "email": "<EMAIL>"
}

Response: 200 OK
{
    "token": "sdfsfsfsf9sf8"
}

```

--->


## Notes
<!---
Insert any relevant notes: warnings, dependencies, required action before/after deploy, future actions that will be taken, how the work continues or any additional info

### :warning: Relevant info
### :rocket: Deploy tasks
### :rotating_light: Dependencies
### :crystal_ball: Future actions
### :information_source: Other notes

--->

<!---
IMPORTANT: Remove sections that don't apply!
--->
