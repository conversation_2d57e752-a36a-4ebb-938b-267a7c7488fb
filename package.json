{"name": "lm-assistant", "version": "2.1.0", "private": true, "dependencies": {"@lottiefiles/dotlottie-react": "^0.9.2", "@reduxjs/toolkit": "^1.5.1", "camelcase-keys": "^9.1.3", "classnames": "^2.3.1", "cookie-universal": "^2.2.2", "env-cmd": "^10.1.0", "final-form": "^4.20.10", "flat": "^5.0.2", "prop-types": "^15.8.0", "react": "^17.0.2", "react-dom": "^17.0.2", "react-error-boundary": "^4.0.13", "react-final-form": "^6.5.9", "react-helmet-async": "^1.0.9", "react-intl": "^5.20.4", "react-redux": "^7.2.3", "react-router-dom": "^5.2.0", "react-scripts": "5.0.0", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "sass": "^1.45.1", "use-long-press": "^3.2.0", "validate.js": "^0.13.1"}, "scripts": {"postinstall": "husky install", "start": "react-scripts start", "dev": "REACT_APP_API_URL=http://localhost:9000 yarn start", "dev:stg": "REACT_APP_API_URL=http://app-sandbox.lavomat.com.uy yarn start", "clean": "rm -rf node_modules/.cache && yarn install", "build": "GENERATE_SOURCEMAP=false react-scripts build", "build:sandbox": "env-cmd -f .env.sandbox yarn build", "deploy:sandbox": "env-cmd -f .env.sandbox yarn build && aws s3 sync build/ s3://assistant-sandbox.lavomat.com.uy --profile lm", "analyze": "source-map-explorer 'build/static/js/*.js'", "build:analyze": "env-cmd -f .env.production react-scripts build", "build-n-analyze": "npm-run-all build:analyze analyze", "test": "react-scripts test", "eject": "react-scripts eject", "lint:code": "eslint src --color", "lint:code:fix": "eslint --fix src", "lint:style": "stylelint \"src/**/*.{css,scss,sass}\"", "lint:style:fix": "stylelint --fix \"src/**/*.{css,scss,sass}\"", "lint": "npm-run-all lint:code lint:style", "lint:fix": "npm-run-all lint:code:fix lint:style:fix", "prettier": "prettier --write './src/**/*.js'"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json}": ["eslint --fix"], "src/**/*.{css,scss,sass}": ["stylelint --fix"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^12.0.0", "autoprefixer": "^10.4.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-json": "^3.1.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-local-rules": "^2.0.1", "eslint-plugin-prettier": "^3.3.1", "husky": "^7.0.4", "lint-staged": "^12.1.4", "npm-run-all": "^4.1.5", "postcss": "^8.4.5", "prettier": "^2.2.1", "source-map-explorer": "^2.5.2", "stylelint": "14.2.0", "stylelint-config-standard": "^24.0.0", "stylelint-config-standard-scss": "^3.0.0", "stylelint-order": "^5.0.0", "tailwindcss": "^3.0.7"}, "jest": {"transformIgnorePatterns": ["node_modules/(?!camelcase-keys)/"]}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}