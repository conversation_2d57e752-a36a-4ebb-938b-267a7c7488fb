{"extends": ["eslint:recommended", "react-app", "react-app/jest", "prettier", "plugin:jsx-a11y/recommended", "plugin:json/recommended"], "plugins": ["react", "prettier", "jsx-a11y", "eslint-plugin-local-rules"], "rules": {"local-rules/tailwind-dynamic-classes": "error", "prettier/prettier": "error", "quotes": ["error", "single", {"avoidEscape": true}], "jsx-quotes": ["error", "prefer-single"], "semi": ["error", "never"], "comma-dangle": ["error", {"arrays": "always-multiline", "objects": "always-multiline", "imports": "always-multiline", "exports": "always-multiline", "functions": "never"}], "no-unused-vars": "error", "no-console": ["error", {"allow": ["error"]}]}}