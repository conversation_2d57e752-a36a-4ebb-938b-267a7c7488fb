import { validate } from 'validate.js'

/**
 * Higher-order function that validates form data against constraints, and returns errors one at a time.
 * @param {object} options.constraints - Constraints object specifying according validate.js expected rules.
 * @param {Function} options.translationFunc - Function for translating error messages, it matches with the key provided at the `message` attribute of constraints.
 * @returns {Function} - Validation function that returns error object containing the first encountered error by field, or null if no errors.
 */
export const validateOneErrorAtTime =
  ({ constraints, translationFunc }) =>
  (data) => {
    const validation = validate(data, constraints, {
      fullMessages: false,
      format: 'detailed',
    })
    if (!validation) return

    const errors = validation.reduce(
      (acc, { attribute, error, options }) => ({
        ...acc,
        // showing one error at a time
        ...(!acc[attribute] && {
          [attribute]: translationFunc(error, options),
        }),
      }),
      {}
    )

    return errors
  }
