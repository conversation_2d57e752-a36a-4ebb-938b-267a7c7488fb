import { useEffect, useMemo } from 'react'
import { useParams } from 'react-router-dom'

import { ButtonIcon } from 'components/common/Button'
import { Conditional, ShowIf } from 'components/common/Conditional'
import { Logo } from 'components/common/Logo'
import { PageTitle } from 'components/common/PageTitle'
import { SkeletonLoader } from 'components/common/SkeletonLoader'
import { Wave } from 'components/common/Wave'
import { useActivation } from 'hooks/useActivation'
import { useMachine } from 'hooks/useMachine'
import { useNavigation } from 'hooks/useNavigation'
import { useTranslation } from 'hooks/useTranslation'

import { useStateMachine } from '../../../contexts/StateMachineContext'
import { APP_ERROR } from '../Error'

export const Main = () => {
  const { t } = useTranslation()
  const { goBack } = useNavigation()
  const params = useParams()
  const { updateSelection, slug, index } = useActivation()

  const { machines, isFetching } = useMachine({
    buildingSlug: params.slug,
  })
  const machine = useMemo(
    () => machines?.find((m) => m.index === +index),
    [machines, index]
  )
  const activationDisabled = !machine || isFetching
  const { reportError, next } = useStateMachine()

  useEffect(() => {
    updateSelection({ slug: params.slug, index: params.index, uid: params.uid })
  }, [params, updateSelection])

  useEffect(() => {
    if (!machine && !isFetching && slug && index) {
      reportError({ appError: APP_ERROR.notAvailableMachine })
    }
  }, [isFetching, machine, reportError, slug, index])

  const handleActivation = () => {
    next({ slug, index })
  }

  return (
    <div className='relative flex flex-col items-center text-center h-screen'>
      <PageTitle
        className='my-10'
        title={t('activation.title')}
        subtitle={t('activation.subtitle')}
      />

      <ButtonIcon
        className='absolute top-0 left-0 mt-5 ml-5'
        icon='back'
        onClick={goBack}
      />

      <Conditional>
        <ShowIf condition={isFetching}>
          <SkeletonLoader className='h-12 w-56 mb-10' />
        </ShowIf>
        <ShowIf isDefault>
          <h2 className='lm__font--60 font-extrabold uppercase text-skyblue mb-10'>
            {t('activation.main.machine', {
              type: machine?.type?.toLowerCase(),
              index: machine?.index,
            })}
          </h2>
        </ShowIf>
      </Conditional>

      <div className='mb-10'>
        <h3 className='lm__font--40 font-light'>{t('activation.main.cost')}</h3>
        <Conditional>
          <ShowIf condition={isFetching}>
            <SkeletonLoader className='h-16 w-36' />
          </ShowIf>
          <ShowIf isDefault>
            <p className='lm__font--70 font-medium uppercase text-skyblue'>
              {t('global.currency.full')}
              {machine?.rate?.priceCustomer}
            </p>
          </ShowIf>
        </Conditional>
      </div>

      <button
        className='w-48 disabled:opacity-80'
        disabled={activationDisabled}
        onClick={handleActivation}
      >
        <Logo className='w-48 h-48 mb-2' />
        <span className='lm__font--40 font-light'>
          {t('activation.main.instruction')}
        </span>
      </button>

      <Wave direction='up' peakCount={3} />
      <Wave direction='up' peakCount={2} color='skyblue' />
    </div>
  )
}
