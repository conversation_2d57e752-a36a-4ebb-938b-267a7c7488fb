import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  card: {},
  uid: undefined,
}

const cardSlice = createSlice({
  name: 'card',
  initialState,
  reducers: {
    setUid: (state, action) => {
      state.uid = action.payload
    },
  },
  extraReducers: (builder) => {
    // TODO: builder.addMatcher(logoutFulfilled, () => initialState)
  },
})

export const { setUid } = cardSlice.actions

export default cardSlice.reducer
