import { createContext, useCallback, useContext, useReducer } from 'react'

export const STEP = {
  TYPE: 'type',
  CAPACITY: 'capacity',
  MAP: 'map',
  LOCATION_TYPE: 'location-type',
  LOCATION: 'location',
}

export const ERROR = {
  EMPTY: 'empty',
}

const initialState = (step) => ({
  step,
  type: undefined,
  capacity: undefined,
  locationType: undefined,
  location: undefined,
  error: undefined,
  errorCount: 0,
})

const StateMapContext = createContext()

export const StateMapProvider = ({
  children,
  init = STEP.TYPE,
  errorTolerance = 1,
}) => {
  const [{ step, type, capacity, error, locationType, location }, dispatch] =
    useReducer((state, action) => {
      if (action.type === 'next') {
        if (state.step === STEP.TYPE) {
          return { ...state, type: action.value, step: STEP.CAPACITY }
        } else if (state.step === STEP.CAPACITY) {
          return { ...state, capacity: action.value, step: STEP.MAP }
        } else if (state.step === STEP.LOCATION_TYPE) {
          return { ...state, locationType: action.value, step: STEP.LOCATION }
        } else if (state.step === STEP.LOCATION) {
          return { ...state, location: action.value, step: STEP.MAP }
        }
      } else if (action.type === 'report' && action.value !== state.error) {
        const errorCount = state.errorCount + 1
        if (errorCount >= errorTolerance) {
          // don't report error until tolerance is met
          return { ...state, error: action.value, errorCount }
        }

        return { ...state, errorCount }
      } else if (action.type === 'reset') {
        return initialState(init)
      }

      return state
    }, initialState(init))

  const advance = useCallback(
    (value) => dispatch({ type: 'next', value }),
    [dispatch]
  )
  const report = useCallback(
    (value) => dispatch({ type: 'report', value }),
    [dispatch]
  )
  const reset = useCallback(() => dispatch({ type: 'reset' }), [dispatch])

  return (
    <StateMapContext.Provider
      value={{
        // states
        step,
        error,
        // filters
        type,
        capacity,
        locationType,
        location,
        // actions
        advance,
        report,
        reset,
      }}
    >
      {children}
    </StateMapContext.Provider>
  )
}

export const useStateMap = () => {
  const context = useContext(StateMapContext)
  if (!context) {
    throw new Error('useStateMap must be used within a StateMapProvider')
  }

  return context
}
