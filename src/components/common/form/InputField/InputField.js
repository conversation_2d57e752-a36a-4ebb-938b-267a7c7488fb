import cn from 'classnames'
import { useState } from 'react'
import { useFormState } from 'react-final-form'

import Icon from 'components/common/Icon'
import useTranslation from 'hooks/useTranslation'

import { FormGroup } from '../FormGroup'

export const InputField = ({
  className = '',
  label,
  required,
  meta,
  input,
  ...props
}) => {
  const { submitErrors = {} } = useFormState()
  const error = meta.error || submitErrors[input.name]
  const touched = (meta.touched || props.touched)?.toString() || 'false'
  const showError = error && touched !== 'false'
  const errorMessage = showError ? error : ''

  return (
    <FormGroup
      className={className}
      error={errorMessage}
      label={label}
      required={required}
      disabled={input.disabled}
      name={input.name}
      value={input.value}
      touched={touched}
    >
      <Input
        {...input}
        {...props}
        error={errorMessage}
        label={label}
        required={required}
        touched={touched}
      />
    </FormGroup>
  )
}

const Input = (props) => {
  const {
    enabled: passwordVisibilityEnabled,
    toggle: togglePasswordVisibility,
    visible: isPasswordVisible,
  } = usePasswordVisibility({
    type: props.type,
  })
  const { t } = useTranslation()

  const classes = cn(
    'lm__form-input lm__font--40 text-center',
    'bg-cornflower w-full rounded-2xl py-3 px-4',
    'bg-cornflower border-2 border-cornflower',
    {
      'lm__form-input--disabled': props.disabled,
      'lm__form-input--error border-blood': !!props.error,
    }
  )

  if (!passwordVisibilityEnabled) {
    return <input {...props} className={classes} />
  }

  return (
    <>
      <input
        {...props}
        className={classes}
        type={isPasswordVisible ? 'text' : props.type}
      />
      <button
        type='button'
        onClick={togglePasswordVisibility}
        className='absolute inset-y-0 right-0 flex items-center px-4 text-gray-600 cursor-pointer	'
      >
        <Icon
          kind={isPasswordVisible ? 'eye-opened' : 'eye-closed'}
          className='w-10 h-10 stroke-blue fill-blue'
          srText={t(
            `global.password.a11y.${isPasswordVisible ? 'hide' : 'show'}`
          )}
        />
      </button>
    </>
  )
}

const usePasswordVisibility = ({ type }) => {
  const [visible, setVisible] = useState(false)

  return {
    enabled: type === 'password',
    toggle: () => setVisible((prev) => !prev),
    visible,
  }
}
