import { useBuilding } from 'hooks/useBuilding'
import { useCard } from 'hooks/useCard'
import { useUserCards } from 'hooks/useUserCards'

export const useWallet = () => {
  const {
    laundromatCards,
    isFetching: isUserCardsFetching,
    isError: isUserCardsError,
  } = useUserCards()
  const [selectedCard] = laundromatCards
  const {
    card,
    isFetching: isCardFetching,
    isError: isCardError,
  } = useCard({
    uid: selectedCard?.uid,
  })
  const { building } = useBuilding()

  return {
    card,
    building,
    isFetching: isUserCardsFetching || isCardFetching,
    isError: isCardError || isUserCardsError,
  }
}
