import { PageTitle } from 'components/common/PageTitle'
import { Warning } from 'components/common/Warning'
import { Wave } from 'components/common/Wave'
import useTranslation from 'hooks/useTranslation'
import { CardQuery } from 'pages/Card/components/CardQuery'

export const NotFoundError = () => {
  const { t } = useTranslation()

  return (
    <div className='flex flex-col items-center'>
      <PageTitle
        className='text-silverChalice mt-10 mb-6'
        title={t('cardDetail.error.notFound.title')}
        titleColor='silverChalice'
      />

      <h2 className='lm__title--sub font-extrabold text-center w-4/5 mb-14'>
        {t('cardDetail.error.notFound.subtitle')}
      </h2>

      <Warning className='mb-14' />

      <CardQuery />

      <Wave direction='up' peakCount={2} height={15} />
      <Wave direction='up' peakCount={3} height={0} color='lightSilver' />
    </div>
  )
}
