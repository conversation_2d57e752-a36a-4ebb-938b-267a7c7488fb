import { useEffect } from 'react'
import { Field, Form as ReactForm } from 'react-final-form'
import { useDispatch } from 'react-redux'

import { Alert } from 'components/common/Alert'
import { ButtonIcon } from 'components/common/Button'
import { InputField } from 'components/common/form'
import { ErrorMessage } from 'components/ErrorMessage'
import { apiResultErrorMessage } from 'constants/apiResultError'
import { httpStatusCode } from 'constants/httpStatusCode'
import useTranslation from 'hooks/useTranslation'
import {
  PASSWORD_REGEX,
  validateOneErrorAtTime,
} from 'lib/common/form/validations'
import { useSignupMutation } from 'services/auth'
import { api } from 'services/base'

const fields = {
  FULL_NAME: 'full-name',
  EMAIL: 'emailAddress',
  PHONE: 'phone',
  PASSWORD: 'password',
  CONFIRM_PASSWORD: 'confirm-password',
}
const constraints = {
  [fields.FULL_NAME]: {
    presence: {
      allowEmpty: false,
      message: 'singup.fields.fullName.error.empty',
    },
  },
  [fields.EMAIL]: {
    presence: {
      allowEmpty: false,
      message: 'singup.fields.email.error.empty',
    },
    email: {
      message: 'singup.fields.email.error.invalid',
    },
  },
  [fields.PHONE]: {
    presence: {
      allowEmpty: false,
      message: 'singup.fields.phone.error.empty',
    },
  },
  [fields.PASSWORD]: {
    presence: {
      allowEmpty: false,
      message: 'singup.fields.email.error.empty',
    },
    length: {
      minimum: 8,
      maximum: 50,
      tooShort: 'singup.fields.password.error.tooShort',
      tooLong: 'singup.fields.password.error.tooLong',
    },
    format: {
      pattern: PASSWORD_REGEX,
      message: 'singup.fields.password.error.pattern',
    },
  },
  [fields.CONFIRM_PASSWORD]: {
    presence: {
      allowEmpty: false,
      message: 'singup.fields.confirmPassword.error.empty',
    },
    equality: {
      attribute: fields.PASSWORD,
      message: 'singup.fields.confirmPassword.error.match',
    },
  },
}

export const Form = ({ onSuccess }) => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const [signup, { isSuccess, error }] = useSignupMutation()

  useEffect(() => dispatch(api.util.resetApiState()), [dispatch])

  useEffect(() => {
    if (isSuccess && onSuccess) onSuccess()
  }, [isSuccess, onSuccess])

  const onSubmit = ({
    [fields.CONFIRM_PASSWORD]: _confirmPassword,
    [fields.FULL_NAME]: fullName,
    ...data
  }) => {
    // workaround to bypass API validations
    const [name, ..._lastName] = fullName.split(' ')
    const lastName = _lastName.join(' ') || 'Apellido'
    const payload = {
      ...data,
      mainAddress: 'N/A',
      country: 'N/A',
      name,
      lastname: lastName,
    }

    return signup(payload)
  }

  return (
    <div className='w-4/5'>
      <ReactForm
        onSubmit={onSubmit}
        validate={validateOneErrorAtTime({ constraints, translationFunc: t })}
        render={({ handleSubmit, submitting }) => (
          <form onSubmit={handleSubmit} className='flex flex-col items-center'>
            <ErrorMessage
              show={!!error}
              apiError={error}
              defaultError={httpStatusCode.BAD_REQUEST}
              mapping={{
                [httpStatusCode.BAD_REQUEST]: BadRequestError,
                [apiResultErrorMessage.USER_ALREADY_EXISTS]:
                  UserAlreadyExistError,
              }}
            />

            <Field
              className='mb-4'
              label={t('singup.fields.fullName.label')}
              component={InputField}
              type='text'
              autoComplete='name'
              name={fields.FULL_NAME}
            />

            <Field
              className='mb-4'
              label={t('singup.fields.email.label')}
              component={InputField}
              type='email'
              autoComplete='email'
              name={fields.EMAIL}
            />

            <Field
              className='mb-4'
              label={t('singup.fields.phone.label')}
              component={InputField}
              type='tel'
              autoComplete='tel'
              name={fields.PHONE}
            />

            <Field
              className='mb-4'
              label={t('singup.fields.password.label')}
              component={InputField}
              type='password'
              autoComplete='new-password'
              name={fields.PASSWORD}
            />

            <Field
              className='mb-12'
              label={t('singup.fields.confirmPassword.label')}
              component={InputField}
              type='password'
              autoComplete='off'
              name={fields.CONFIRM_PASSWORD}
            />

            <ButtonIcon
              type='submit'
              loading={submitting}
              disabled={submitting}
            />
          </form>
        )}
      />
    </div>
  )
}

const BadRequestError = () => {
  const { t } = useTranslation()

  return <Alert>{t('singup.error.generic')}</Alert>
}

const UserAlreadyExistError = () => {
  const { t } = useTranslation()

  return <Alert>{t('singup.error.userAlreadyExist')}</Alert>
}
