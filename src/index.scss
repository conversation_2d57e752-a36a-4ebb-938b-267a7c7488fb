@font-face {
  font-family: "Rounded Mplus 1c";
  font-weight: 100;
  src:
    local("Rounded Mplus 1c"),
    url(./assets/fonts/RoundedMplus1c-Thin.ttf) format("truetype");
}

@font-face {
  font-family: "Rounded Mplus 1c";
  font-weight: 300;
  src:
    local("Rounded Mplus 1c"),
    url(./assets/fonts/RoundedMplus1c-Light.ttf) format("truetype");
}

@font-face {
  font-family: "Rounded Mplus 1c";
  font-weight: 400;
  src:
    local("Rounded Mplus 1c"),
    url(./assets/fonts/RoundedMplus1c-Regular.ttf) format("truetype");
}

@font-face {
  font-family: "Rounded Mplus 1c";
  font-weight: 500;
  src:
    local("Rounded Mplus 1c"),
    url(./assets/fonts/RoundedMplus1c-Medium.ttf) format("truetype");
}

@font-face {
  font-family: "Rounded Mplus 1c";
  font-weight: 700;
  src:
    local("Rounded Mplus 1c"),
    url(./assets/fonts/RoundedMplus1c-Bold.ttf) format("truetype");
}

@font-face {
  font-family: "Rounded Mplus 1c";
  font-weight: 800;
  src:
    local("Rounded Mplus 1c"),
    url(./assets/fonts/RoundedMplus1c-ExtraBold.ttf) format("truetype");
}

@font-face {
  font-family: "Rounded Mplus 1c";
  font-weight: 900;
  src:
    local("Rounded Mplus 1c"),
    url(./assets/fonts/RoundedMplus1c-Black.ttf) format("truetype");
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@import "styles/variables.scss";
@import "styles/utilities.scss";
@import "styles/form.scss";

html {
  font-size: 16px;
  margin: 0;
  padding: 0;
}

body {
  color: $color-blue;
  font-family: "Rounded Mplus 1c", sans-serif;
  margin: 0;
  padding: 0;
}
