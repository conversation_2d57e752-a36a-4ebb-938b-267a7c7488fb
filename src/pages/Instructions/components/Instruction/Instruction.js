import cn from 'classnames'

import Icon from 'components/common/Icon'
import useTranslation from 'hooks/useTranslation'

export const Instruction = ({
  method = 'washing',
  icon,
  hasDetails,
  isLaundromat,
}) => {
  const { t } = useTranslation()

  return (
    <div className='flex flex-col items-center mb-10'>
      <Icon
        kind={icon}
        color={method === 'washing' ? 'skyblue' : 'blue'}
        className={cn({
          'w-16': icon !== 'dispenser',
          'h-16': icon !== 'soapBrands',
          'w-44': icon === 'soapBrands',
        })}
      />
      <p className='lm__font--40 font-light text-center px-10'>
        {t(`instructions.${method}.items.${icon}.message`, {
          experience: isLaundromat ? 'laundromat' : 'building',
        })}
      </p>
      {hasDetails && (
        <p className='lm__font--30 font-light text-center px-10'>
          {t(`instructions.${method}.items.${icon}.detail`, { br: <br /> })}
        </p>
      )}
    </div>
  )
}
