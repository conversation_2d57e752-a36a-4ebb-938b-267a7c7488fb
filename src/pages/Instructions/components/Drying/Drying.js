import { SectionTitle } from 'components/common/SectionTitle'
import { Instruction } from '../Instruction'
import { useExperience } from 'lib/experience'

export const Drying = () => (
  <div className='flex flex-col items-center'>
    <SectionTitle intlId={'instructions.drying.title'} className='my-10' />
    <Instructions />
  </div>
)

const data = ['on', 'filter', 'start', 'clock']
const Instructions = () => {
  const { isLaundromat } = useExperience()

  return data.map((key, index) => (
    <Instruction
      key={`drying-${index}`}
      method='drying'
      icon={key}
      hasDetails={key === 'clock'}
      isLaundromat={isLaundromat}
    />
  ))
}
