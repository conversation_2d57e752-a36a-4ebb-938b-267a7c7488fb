/**
 * [Flag] Indicates if it is development environment
 */
export const DEV_ENV = process.env.NODE_ENV === 'development'

/**
 * [Flag] Indicates if the forgot-password feature is enabled
 */
export const FORGOT_PASSWORD_ENABLED =
  process.env.REACT_APP_FORGOT_PASSWORD_ENABLED === 'true'

/**
 * [String value] Public site URL
 */
export const PUBLIC_SITE_URL =
  process.env.REACT_APP_PUBLIC_SITE_URL || 'www.lavomat.com.uy'

/**
 * [String value] Public site URL where purchase credits
 */
export const PUBLIC_SITE_PURCHASE_CREDITS_PATH =
  process.env.REACT_APP_PUBLIC_SITE_PURCHASE_CREDITS_PATH || '/credit?card='

export const getPublicSiteData = () => {
  let url = PUBLIC_SITE_URL
  let text = PUBLIC_SITE_URL.replace(/^(https?):\/\//, '')
  if (!url.startsWith('http')) {
    url = `https://${url}`
  }

  let purchaseCreditsUrl = PUBLIC_SITE_PURCHASE_CREDITS_PATH
  if (!purchaseCreditsUrl.startsWith('/')) {
    purchaseCreditsUrl = `/${purchaseCreditsUrl}`
  }
  purchaseCreditsUrl = `${url}${purchaseCreditsUrl}`

  return { text, url, purchaseCreditsUrl }
}

/**
 * [String value] Google reCAPTCHA site key
 */
export const RECAPTCHA_KEY = process.env.REACT_APP_RECAPTCHA_KEY

/**
 * [String value] Survey link
 */
export const SURVEY_LINKS = {
  building:
    process.env.REACT_APP_SURVEY_LINK_BUILDING ||
    'https://forms.gle/Gm2pPEAMqfJt5qCKA',
  laundromat:
    process.env.REACT_APP_SURVEY_LINK_LAUNDROMAT ||
    'https://forms.gle/HncuTjB3ntMstU3AA',
}

/**
 * [Flag] Indicates if the activation feature is enabled
 */
export const ACTIVATION_FEATURE_ENABLED =
  process.env.REACT_APP_ACTIVATION_FEATURE_ENABLED === 'true'

/**
 * [Flag] Indicates if the laundromat only experience is enabled
 */
export const LAUNDROMAT_ONLY_EXPERIENCE_FEATURES = false
