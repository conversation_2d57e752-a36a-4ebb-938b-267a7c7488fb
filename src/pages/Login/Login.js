import { Link, useLocation } from 'react-router-dom'

import { Logo } from 'components/common/Logo'
import { Wave } from 'components/common/Wave'
import { FORGOT_PASSWORD_ENABLED } from 'constants/configurations'
import { useBuilding } from 'hooks/useBuilding'
import { useNavigation } from 'hooks/useNavigation'
import { useTranslation } from 'hooks/useTranslation'
import { hashes, paths } from 'router/paths'

import { Form } from './components/Form'

export const Login = ({ as = 'page' }) => {
  const { t } = useTranslation()
  const location = useLocation()
  const { goTo } = useNavigation()
  const { slug } = useBuilding()

  const handleLoggedIn = () => {
    let nextPath = location.state?.from
    if (!nextPath) nextPath = paths.indexWithBuilding(slug, hashes.map)
    if (!nextPath) nextPath = paths.index

    goTo(nextPath)
  }

  const Title = as === 'section' ? 'h2' : 'h1'

  return (
    <div className='relative flex flex-col items-center min-h-screen'>
      <Title className='my-10 lm__font--50 uppercase text-skyblue text-center flex flex-row items-center'>
        <Logo className='w-12 h-12 mr-2' />

        {t('login.title')}
      </Title>

      <Form onSuccess={handleLoggedIn} />

      <ForgotPasswordLink />
      <SignUpLink />

      <Wave direction='up' peakCount={3} color='cornflower' />
    </div>
  )
}

const ForgotPasswordLink = () => {
  const { t } = useTranslation()

  if (!FORGOT_PASSWORD_ENABLED) return null

  return (
    <h2 className='lm__font--30 font-light mt-12 underline'>
      <Link to={paths.forgotPassword}>{t('login.forgotPassword.link')}</Link>
    </h2>
  )
}

const SignUpLink = () => {
  const { t } = useTranslation()

  return (
    <div className='text-center'>
      <h2 className='lm__font--50 font-light mt-12'>
        {t('login.singUp.question')}
      </h2>
      <Link to={paths.singUp} className='lm__font--50 font-extrabold'>
        {t('login.singUp.link')}
      </Link>
    </div>
  )
}
