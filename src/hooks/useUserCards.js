import { useGetUserCardsQuery } from 'services/card'
import useAuth from './useAuth'

export const useUserCards = () => {
  const { authenticated } = useAuth()
  const {
    data: cards,
    isUninitialized,
    isFetching,
    ...rest
  } = useGetUserCardsQuery({ level: 2 }, { skip: !authenticated })
  const laundromatCards =
    cards?.filter((card) => card.belongsToLaundromat) || []
  const buildingCards = cards?.filter((card) => !card.belongsToLaundromat) || []

  return {
    cards,
    laundromatCards,
    buildingCards,
    wasQueryMade: !isUninitialized && !isFetching,
    isUninitialized,
    isFetching,
    ...rest,
  }
}
