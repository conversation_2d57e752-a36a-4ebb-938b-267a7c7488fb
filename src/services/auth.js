import { endpoints } from 'services/endpoints'

import { injectEndpoints } from './base'

import { AUTH_REMEMBER_USER_KEY } from 'constants/constants'
import { remindUser, resetRemindedUser, setLoggedInUser } from 'utils/auth'

export const authApi = injectEndpoints({
  endpoints: (builder) => ({
    signup: builder.mutation({
      query: (user) => ({
        url: endpoints.SIGN_UP,
        method: 'POST',
        body: user,
      }),
    }),
    login: builder.mutation({
      query: (user) => ({
        url: endpoints.SIGN_IN,
        method: 'POST',
        body: user,
      }),
      transformResponse: (response, _meta, args) => {
        const payload = {
          token: response?.token,
          data: response?.account?.user,
        }
        setLoggedInUser(payload)
        if (args?.[AUTH_REMEMBER_USER_KEY]) {
          remindUser(payload.data)
        } else {
          resetRemindedUser()
        }

        return payload
      },
    }),
    logout: builder.mutation({
      query: () => ({
        url: endpoints.SIGN_OUT,
        method: 'DELETE',
      }),
      // TODO: clean local storage data
    }),
  }),
})

export const {
  useSignupMutation,
  useLoginMutation,
  useLogoutMutation,
  endpoints: {
    signup: { matchFulfilled: signupFulfilled },
    login: { matchFulfilled: loginFulfilled },
    logout: { matchFulfilled: logoutFulfilled },
  },
} = authApi

export const selectAuth = (state) => state.auth
