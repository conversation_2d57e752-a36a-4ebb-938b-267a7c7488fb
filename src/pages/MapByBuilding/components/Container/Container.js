import { ERROR, useStateMap } from '../../hooks/useStateMap'
import { MapError } from '../MapError'
import { EmptyPlaceholder } from '../MapError/EmptyError'

export const Container = ({ children, className = '' }) => {
  const { error } = useStateMap()

  return (
    <>
      <div className={`flex flex-col items-center h-full ${className}`}>
        {children}
        {error === ERROR.EMPTY && <EmptyPlaceholder />}
      </div>
      <MapError />
    </>
  )
}
