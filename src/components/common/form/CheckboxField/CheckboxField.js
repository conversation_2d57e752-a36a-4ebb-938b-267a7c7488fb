import { useFormState } from 'react-final-form'
import cn from 'classnames'

import { FormGroup } from '../FormGroup'

export const CheckboxField = ({
  className = '',
  label,
  required,
  meta,
  input,
  ...props
}) => {
  const { submitErrors = {} } = useFormState()
  const error = meta.error || submitErrors[input.name]
  const touched = (meta.touched || props.touched)?.toString() || 'false'

  return (
    <FormGroup
      className={className}
      error={error}
      required={required}
      disabled={input.disabled}
      name={input.name}
      value={input.value}
      touched={touched}
    >
      <Checkbox
        {...input}
        {...props}
        error={error}
        label={label}
        required={required}
        touched={touched}
      />
    </FormGroup>
  )
}

const Checkbox = ({
  className = '',
  label,
  name,
  checked,
  ariaLabel,
  ...props
}) => {
  const classes = cn(
    className,
    'lm__form-input lm__form-input__checkbox lm__font--40',
    'flex justify-center items-center',
    'w-full text-center',
    'cursor-pointer select-none',
    {
      'lm__form-input__checkbox--checked': checked,
      'lm__form-input--disabled': props.disabled,
      'lm__form-input--error': !!props.error,
    }
  )

  return (
    <label className={classes}>
      <input type='checkbox' className='sr-only' {...props} readOnly />
      <span className='rounded border-solid	border-white' aria-hidden='true' />
      {label}
    </label>
  )
}
