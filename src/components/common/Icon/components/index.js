import { Check } from './Check'
import { Chevron } from './Chevron'
import { Clock } from './Clock'
import { Close } from './Close'
import { Cloth } from './Cloth'
import { Coin } from './Coin'
import { Dispenser } from './Dispenser'
import { Drop } from './Drop'
import { EyeClosed } from './EyeClosed'
import { EyeOpened } from './EyeOpened'
import { Filter } from './Filter'
import { Logo } from './Logo'
import { Machine } from './Machine'
import { Menu } from './Menu'
import { On } from './On'
import { Plus } from './Plus'
import { SoapBrands } from './SoapBrands'
import { Start } from './Start'
import { Trash } from './Trash'
import { Wind } from './Wind'

export const icons = [
  Check,
  Chevron,
  Clock,
  Close,
  Cloth,
  Coin,
  Dispenser,
  Drop,
  EyeClosed,
  EyeOpened,
  Filter,
  Logo,
  Machine,
  Menu,
  On,
  Plus,
  SoapBrands,
  Start,
  Trash,
  Wind,
].reduce((acc, icon) => ({ ...acc, [icon]: icon }), {})
