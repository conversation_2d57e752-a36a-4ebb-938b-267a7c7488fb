module.exports = {
  'tailwind-dynamic-classes': {
    meta: {
      type: 'problem',
      docs: {
        description:
          'String interpolation or concatenate partial class names is not allowed',
        url: 'https://tailwindcss.com/docs/content-configuration#dynamic-class-names',
      },
      schema: [], // no options
    },
    create: function (context) {
      return {
        TemplateLiteral(node) {
          const variables = node.expressions.filter(
            (e) => e.type === 'Identifier'
          )

          let value = ''
          for (const quasi of node.quasis) {
            const variable = variables.find((v) => v.start === quasi.end)
            value += quasi.value.raw
            if (variable) {
              value += '${'
              value += variable.name
              value += '}'
            }
          }

          if (
            node?.parent?.parent?.type === 'JSXAttribute' &&
            node?.parent?.parent?.name?.name !== 'className'
          ) {
            return
          }

          const regex = /(?<=\b|-)\$\{[^}]+\}/g
          if (value.match(regex)) {
            context.report({
              node,
              message: `Avoid using dynamic class names that break Tailwind CSS rules: ${value}`,
            })
          }
        },
      }
    },
  },
}

// Testing case
//
// import React from 'react'
// import cn from 'classnames'
// const index = 0
// export const Component = ({ color = 'black', className = '' }) => {
//   // reported ok cases - result === expected
//   const classes = cn(`bg-${color} mb-2`)

//   return (
//     <div>
//       {/* reported ok cases - result === expected */}
//       <p className={`bg-${color} mb-2`}>Compoent</p>
//       <p className={`text-white bg-${color} mb-2`}>Compoent</p>
//       <p className={`text-white bg-${color} mb-2`}>Compoent</p>
//       <p className={`text-white bg-${color}-red mb-2`}>Compoent</p>
//       {/* non-reported ok cases - result === expected */}
//       <p className={`${className} lm__other`}>Compoent</p>
//       <p className={`lm__other ${className}`}>Compoent</p>
//       <p className={classes}>Compoent</p>
//       <p id={`index-${index}`}>Compoent</p>
//       <p className={`${className} lm-other lm-other--page`}>Compoent</p>

//       {/* pending */}
//       <p className={`text-white ${color}-2 mb-2`}>Compoent</p>
//     </div>
//   )
// }
