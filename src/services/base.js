import { createApi } from '@reduxjs/toolkit/query/react'

import { baseQueryWithReauth } from 'utils/customQueries'

export const tags = {
  USER_CARDS: 'user_cards',
}

export const api = createApi({
  baseQuery: baseQueryWithReauth,
  endpoints: () => ({}),
  tagTypes: Object.values(tags),
})

export const injectEndpoints = (opts) =>
  api.injectEndpoints({
    overrideExisting: false,
    ...opts,
  })
