import cn from 'classnames'
import { useMemo } from 'react'

import { MACHINE } from 'constants/models/machine'
import { useTranslation } from 'hooks/useTranslation'

import { useStateMap } from '../../../hooks/useStateMap'
import { Machine } from '../../Machine'

import classes from '../../Machines/Machines.module.scss'
import { ONE_MINUTE } from 'constants/time'

/**
 * Returns machines with random data
 */
const getMachines = (type) => {
  // random number between 2 and 8
  const length = Math.floor(Math.random() * 7) + 2

  const generateMachine = (_, index) => {
    const status =
      Math.random() > 0.5 ? MACHINE.STATUS.AVAILABLE : MACHINE.STATUS.IN_USE
    // random minutes between 10 and 50
    const remainingTime = (Math.floor(Math.random() * 40) + 10) * ONE_MINUTE

    return {
      index: index + 1,
      type,
      status,
      remainingTime: status === MACHINE.STATUS.IN_USE ? remainingTime : null,
    }
  }

  return Array.from({ length }, generateMachine)
}

export const EmptyPlaceholder = () => {
  const { t } = useTranslation()
  const { type: selectedType } = useStateMap()

  const { machineType, machines } = useMemo(() => {
    const type =
      selectedType === MACHINE.TYPE.WASHER ? selectedType : MACHINE.TYPE.DRYER
    return { machineType: type, machines: getMachines(type) }
  }, [selectedType])

  return (
    <div className='flex flex-col w-3/5'>
      <h2
        className={`${
          machineType === MACHINE.TYPE.WASHER ? 'text-skyblue' : 'text-blue'
        } lm__font--60 font-normal uppercase`}
      >
        {t(`mapByBuilding.${machineType}`)}
      </h2>
      <div
        className={cn(
          classes['machines-container'],
          classes['machines-container--4']
        )}
      >
        {machines.map((machine) => (
          <Machine
            key={machine.index}
            machine={machine}
            onAvailableSelected={() => {}}
          />
        ))}
      </div>
    </div>
  )
}
