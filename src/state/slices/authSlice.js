import { createSlice, isAnyOf } from '@reduxjs/toolkit'
import { persistReducer } from 'redux-persist'
import storage from 'redux-persist/lib/storage'

import { loginFulfilled, logoutFulfilled } from 'services/auth'
import { cleanLoggedInUser, redirectToLogin } from 'utils/auth'

const initialState = {
  authenticated: false,
  token: undefined,
  user: {},
}

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser(state, { payload: { token, data } }) {
      state.authenticated = !!token
      state.token = token
      state.user = data
    },
    logout(state, { payload: { forceLogin } }) {
      state.authenticated = false
      state.token = undefined
      state.user = {}
      cleanLoggedInUser()
      if (forceLogin) redirectToLogin()
    },
  },
  extraReducers: (builder) => {
    builder.addMatcher(
      isAnyOf(loginFulfilled),
      (state, { payload: { data, token, ...rest } }) => {
        state.authenticated = !!token
        state.token = token
        state.user = {
          ...data,
          ...rest,
        }
      }
    )
    builder.addMatcher(logoutFulfilled, () => initialState)
  },
})

const persistConfig = {
  key: 'auth',
  storage,
  whitelist: ['authenticated', 'token', 'user'],
}

export const { setUser, logout } = authSlice.actions

export default persistReducer(persistConfig, authSlice.reducer)
