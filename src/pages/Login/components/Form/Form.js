import { useEffect } from 'react'
import { Field, Form as ReactForm } from 'react-final-form'
import { useDispatch } from 'react-redux'

import { Alert } from 'components/common/Alert'
import { ButtonIcon } from 'components/common/Button'
import { CheckboxField, InputField } from 'components/common/form'
import { ErrorMessage } from 'components/ErrorMessage'
import { AUTH_REMEMBER_USER_KEY } from 'constants/constants'
import { httpStatusCode } from 'constants/httpStatusCode'
import { useRecaptcha } from 'hooks/useRecaptcha'
import useTranslation from 'hooks/useTranslation'
import { validateOneErrorAtTime } from 'lib/common/form/validations'
import { useLoginMutation } from 'services/auth'
import { api } from 'services/base'
import { getRemindedUser } from 'utils/auth'

const fields = {
  EMAIL: 'emailAddress',
  PASSWORD: 'password',
  REMEMBER_ME: AUTH_REMEMBER_USER_KEY,
}
const constraints = {
  [fields.EMAIL]: {
    presence: {
      allowEmpty: false,
      message: 'login.fields.email.error.empty',
    },
  },
  [fields.PASSWORD]: {
    presence: {
      allowEmpty: false,
      message: 'login.fields.password.error.empty',
    },
  },
}

export const Form = ({ onSuccess }) => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const [login, { isSuccess, error }] = useLoginMutation()
  const remindedEmail = getRemindedUser()
  const {
    getToken,
    isReady: isRecaptchaReady,
    token: recaptchaToken,
    isFetching: isRecaptchaFetching,
  } = useRecaptcha()

  useEffect(() => dispatch(api.util.resetApiState()), [dispatch])

  useEffect(() => {
    if (isRecaptchaReady) getToken()
  }, [isRecaptchaReady, getToken])

  useEffect(() => {
    if (isSuccess && onSuccess) onSuccess()
  }, [isSuccess, onSuccess])

  const onSubmit = (data) => login({ ...data, recaptchaToken })

  return (
    <div className='w-4/5'>
      <ReactForm
        initialValues={{ [fields.EMAIL]: remindedEmail }}
        onSubmit={onSubmit}
        validate={validateOneErrorAtTime({ constraints, translationFunc: t })}
        render={({ handleSubmit, submitting }) => (
          <form onSubmit={handleSubmit} className='flex flex-col items-center'>
            <ErrorMessage
              show={!!error}
              apiError={error}
              defaultError={httpStatusCode.UNAUTHORIZED}
              mapping={{
                [httpStatusCode.UNAUTHORIZED]: UnauthorizedError,
              }}
            />

            <Field
              className='mb-4'
              label={t('login.fields.email.label')}
              component={InputField}
              type='email'
              autoComplete='email'
              name={fields.EMAIL}
            />

            <Field
              className='mb-4'
              label={t('login.fields.password.label')}
              component={InputField}
              type='password'
              autoComplete='current-password'
              name={fields.PASSWORD}
            />

            <Field
              className='mb-4'
              label={t('login.fields.rememberMe.label')}
              component={CheckboxField}
              type='checkbox'
              name={fields.REMEMBER_ME}
            />

            <ButtonIcon
              type='submit'
              loading={submitting}
              disabled={submitting || isRecaptchaFetching}
            />
          </form>
        )}
      />
    </div>
  )
}

const UnauthorizedError = () => {
  const { t } = useTranslation()

  return <Alert>{t('login.error.unauthorized')}</Alert>
}
