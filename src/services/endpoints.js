const session = {
  SIGN_UP: '/api/v1/signup',
  SIGN_IN: '/asst/v1/signin',
  SIGN_OUT: '/users/sign_out',
}

const activation = {
  ACTIVATE_MACHINE: '/asst/v1/activate',
}

const building = {
  GET_BUILDING: '/asst/v1/buildings/:slug',
  GET_LAUNDROMATS: '/asst/v1/buildings/laundromats',
  GET_PRICING: '/asst/v1/buildings/:slug/pricing',
}

const card = {
  GET_CARD: '/asst/v1/cards/:uid',
  GET_ACTIVITY: '/asst/v1/cards/:uid/activity',
  GET_USERS_CARDS: '/asst/v1/users/cards',
  REGISTER_USER_CARD: '/asst/v1/users/cards',
  DELETE_USER_CARD: '/asst/v1/users/cards/:uid',
}

const machine = {
  GET_MACHINE: '/asst/v1/buildings/:slug/machines',
}

export const endpoints = {
  ...activation,
  ...building,
  ...card,
  ...machine,
  ...session,
}

export const buildEndpoint = (path, params, query) => {
  // replace paths params by value => cards/:uid -> cards/123
  let endpoint = [...path.matchAll(/:(\w+)/g)].reduce((acc, [param, key]) => {
    return acc.replace(param, params[key] || '')
  }, path)

  // add query string => ?key=value
  if (query) {
    // filter undefined values
    const sanitizedQuery = Object.fromEntries(
      Object.entries(query).filter(([, value]) => value !== undefined)
    )

    const queryString = new URLSearchParams(sanitizedQuery).toString()
    endpoint += `?${queryString}`
  }

  return endpoint
}
