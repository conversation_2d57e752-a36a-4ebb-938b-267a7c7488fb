import { MemoryRouter } from 'react-router-dom'

import { paths } from 'router/paths'
import { Routes } from 'router/Routes'

import { render, screen } from './setupTests'

test('renders Homepage', async () => {
  render(
    <MemoryRouter initialEntries={['/']}>
      <Routes />
    </MemoryRouter>
  )

  expect(
    await screen.findByRole(
      'heading',
      { name: 'Bienvenido' },
      { timeout: 5000 }
    )
  ).toBeInTheDocument()
})

test.each(
  Object.values(paths).map((path) =>
    typeof path === 'function' ? path() : path
  )
)('renders route "%s" without errors', async (path) => {
  render(
    <MemoryRouter initialEntries={[path]}>
      <Routes />
    </MemoryRouter>
  )

  // Wait for heading to appear after loading
  expect(await screen.findByRole('heading', { level: 1 })).toBeInTheDocument()

  expect(
    screen.queryByText(/Ocurrió un error inesperado en el sitio/)
  ).not.toBeInTheDocument()
})
