name: Sanity check

# This workflow is triggered when a pull request is opened, updated, or reopened
# It performs sanity checks including linting and testing on the proposed changes.
on:
  pull_request:
    branches:
      - main

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - uses: actions/cache@v4
        with:
          path: '**/node_modules'
          key: ${{ runner.os }}-modules-${{ hashFiles('**/yarn.lock') }}

      - name: Configure node version
        uses: actions/setup-node@v2
        with:
          node-version: '16.13.1'

      - name: Install dependencies
        run: |
          yarn

      - name: Lint
        run: |
          yarn lint

      - name: Testing
        run: |
          yarn test
