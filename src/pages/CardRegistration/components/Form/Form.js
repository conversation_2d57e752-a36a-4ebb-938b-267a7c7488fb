import { useEffect } from 'react'
import { Field, Form as ReactForm } from 'react-final-form'

import { Alert } from 'components/common/Alert'
import { Button } from 'components/common/Button'
import { InputField } from 'components/common/form'
import { ErrorMessage } from 'components/ErrorMessage'
import { apiResultErrorMessage } from 'constants/apiResultError'
import { httpStatusCode } from 'constants/httpStatusCode'
import { useTranslation } from 'hooks/useTranslation'
import { validateOneErrorAtTime } from 'lib/common/form/validations'
import { useRegisterUserCardMutation } from 'services/card'

const fields = {
  UID: 'uid',
  ALIAS: 'alias',
}

const constraints = {
  [fields.UID]: {
    presence: {
      allowEmpty: false,
      message: 'cardRegistration.fields.uid.error.empty',
    },
  },
  [fields.ALIAS]: {
    presence: {
      allowEmpty: false,
      message: 'cardRegistration.fields.alias.error.empty',
    },
  },
}

export const Form = ({ onSuccess }) => {
  const { t } = useTranslation()
  const [registerCard, { isSuccess, error }] = useRegisterUserCardMutation()

  useEffect(() => {
    if (isSuccess && onSuccess) onSuccess()
  }, [isSuccess, onSuccess])

  const onSubmit = (data) => {
    return registerCard(data)
  }

  return (
    <div className='w-4/5'>
      <ReactForm
        onSubmit={onSubmit}
        validate={validateOneErrorAtTime({ constraints, translationFunc: t })}
        render={({ handleSubmit, submitting }) => (
          <form onSubmit={handleSubmit} className='flex flex-col items-center'>
            <ErrorMessage
              show={!!error}
              apiError={error}
              defaultError={httpStatusCode.BAD_REQUEST}
              mapping={{
                [httpStatusCode.BAD_REQUEST]: BadRequestError,
                [apiResultErrorMessage.CARD_NOT_FOUND]: CardNotFoundError,
              }}
            />

            <Field
              className='mb-4'
              label={t('cardRegistration.fields.uid.label')}
              component={InputField}
              type='text'
              name={fields.UID}
            />

            <Field
              className='mb-12'
              label={t('cardRegistration.fields.alias.label')}
              component={InputField}
              type='text'
              name={fields.ALIAS}
            />

            <Button type='submit' loading={submitting} disabled={submitting}>
              {t('cardRegistration.cta')}
            </Button>
          </form>
        )}
      />
    </div>
  )
}

const BadRequestError = () => {
  const { t } = useTranslation()

  return <Alert>{t('cardRegistration.error.generic')}</Alert>
}

const CardNotFoundError = () => {
  const { t } = useTranslation()

  return <Alert>{t('cardRegistration.error.cardNotFound')}</Alert>
}
