import { lazy } from 'react'
import { Redirect } from 'react-router-dom'

import { useExperience } from 'lib/experience'

import { useBuilding } from 'hooks/useBuilding'
import { paths } from 'router/paths'
import { MapBuildingProvider } from './hooks/useMapBuilding'
import { StateMapProvider, STEP } from './hooks/useStateMap'

export const LOCATION = {
  homepage: 'homepage',
  map: 'map',
}

const LoggedInLaundromatMap = lazy(() =>
  import(
    /* webpackChunkName: "llm-exp" */ './experiences/LoggedInLaundromatMap'
  )
)

const LoggedInUnknownMap = lazy(() =>
  import(
    /* webpackChunkName: "logged-in-exp" */ './experiences/LoggedInUnknownMap'
  )
)

const LoggedInBuildingMap = lazy(() =>
  import(/* webpackChunkName: "lbd-exp" */ './experiences/LoggedInBuildingMap')
)

const DefaultMap = lazy(() => import('./experiences/DefaultMap'))

export const MapByBuilding = ({ location = LOCATION.map }) => {
  const {
    isLoggedInLaundromat,
    isLoggedInUnknown,
    isLoggedInBuilding,
    isActivationEnabled,
  } = useExperience()
  const { slug } = useBuilding()

  const [component, config = {}] = (() => {
    if (isLoggedInLaundromat) {
      return [<LoggedInLaundromatMap location={location} />]
    } else if (isLoggedInUnknown) {
      return [
        <LoggedInUnknownMap location={location} />,
        { step: STEP.LOCATION_TYPE },
      ]
    } else if (isLoggedInBuilding) {
      return [
        <LoggedInBuildingMap location={location} />,
        {
          // Skip error reporting for one empty washer section.
          // Buildings may have only soap washers, only non-soap washers, or both types
          errorTolerance: 2,
        },
      ]
    }
    return [<DefaultMap location={location} />]
  })()

  if (!isActivationEnabled) {
    if (location === LOCATION.map) {
      return <Redirect to={paths.indexWithBuilding(slug)} />
    }

    return null
  }

  return (
    <div className='relative h-screen'>
      <MapBuildingProvider>
        <StateMapProvider {...config}>{component}</StateMapProvider>
      </MapBuildingProvider>
    </div>
  )
}
