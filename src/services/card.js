import { DEFAULT_QUERY_LEVEL } from 'constants/constants'
import { endpoints, buildEndpoint } from 'services/endpoints'

import { injectEndpoints, tags } from './base'

export const cardApi = injectEndpoints({
  endpoints: (builder) => ({
    getCard: builder.query({
      query: ({ uid, level = DEFAULT_QUERY_LEVEL, recaptchaToken }) => ({
        url: buildEndpoint(
          endpoints.GET_CARD,
          { uid },
          { level, recaptchaToken }
        ),
        method: 'GET',
      }),
    }),
    getUserCards: builder.query({
      query: ({ level = DEFAULT_QUERY_LEVEL }) => ({
        url: buildEndpoint(endpoints.GET_USERS_CARDS, {}, { level }),
        method: 'GET',
      }),
      providesTags: [tags.USER_CARDS],
    }),
    getActivity: builder.query({
      query: ({ uid, building, level = DEFAULT_QUERY_LEVEL }) => ({
        url: buildEndpoint(
          endpoints.GET_ACTIVITY,
          { uid },
          { building, level }
        ),
        method: 'GET',
      }),
    }),
    registerUserCard: builder.mutation({
      query: (data) => ({
        url: buildEndpoint(endpoints.REGISTER_USER_CARD),
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [tags.USER_CARDS],
    }),
    deleteUserCard: builder.mutation({
      query: ({ uid }) => ({
        url: buildEndpoint(endpoints.DELETE_USER_CARD, { uid }),
        method: 'DELETE',
      }),
      invalidatesTags: [tags.USER_CARDS],
    }),
  }),
})

export const {
  useGetCardQuery,
  useGetUserCardsQuery,
  useGetActivityQuery,
  useRegisterUserCardMutation,
  useDeleteUserCardMutation,
} = cardApi

export const selectCard = (state) => state.card.card
export const selectUid = (state) => state.card.uid
