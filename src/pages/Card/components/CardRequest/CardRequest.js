import { ButtonNavHome } from 'components/ButtonNavHome'
import useTranslation from 'hooks/useTranslation'
import { hashes } from 'router/paths'

export const CardRequest = () => {
  const { t } = useTranslation()

  return (
    <div className='flex flex-col items-center'>
      <h3 className='lm__font--50 font-light text-center w-3/4 mb-4'>
        {t('card.request.title')}
      </h3>

      <ButtonNavHome hash={hashes.virtualAssistant}>
        {t('card.request.cta')}
      </ButtonNavHome>
    </div>
  )
}
