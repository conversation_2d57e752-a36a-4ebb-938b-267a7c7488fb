import { SectionTitle } from 'components/common/SectionTitle'
import { BUILDING } from 'constants/models/building'
import { useTranslation } from 'hooks/useTranslation'

import { useStateMap } from '../../hooks/useStateMap'

import classes from './LocationFilters.module.scss'

export const LocationFilters = ({ onSelect, locations = [] }) => {
  const { t } = useTranslation()
  const { locationType } = useStateMap()

  return (
    <div className='flex flex-col items-center justify-around h-full'>
      <div className='mt-2'>
        <SectionTitle
          intlId={`mapByBuilding.filters.location.${locationType}.title`}
        />
        {locationType === BUILDING.TYPE.LAUNDROMAT && (
          <p className='lm__font--40 font-light text-center px-10'>
            {t(`mapByBuilding.filters.location.${locationType}.subtitle`)}
          </p>
        )}
      </div>

      <Buildings buildings={locations} onSelect={onSelect} />
    </div>
  )
}

export const Buildings = ({ buildings = [], onSelect }) => (
  <div className={`w-full ${classes.container}`}>
    <div className='flex flex-col items-center justify-center'>
      {buildings.map((building) => (
        <Building key={building.slug} building={building} onClick={onSelect} />
      ))}
    </div>
  </div>
)

const Building = ({ building = {}, onClick }) => {
  const { t } = useTranslation()

  const { name, city } = building

  return (
    <button
      onClick={() => onClick?.(building)}
      className='flex flex-col items-center bg-cornflower mb-6 last:mb-0 py-4 w-4/5 rounded-2xl'
    >
      <span className='uppercase lm__font--30 mb-2'>{t('map.building')}</span>
      <h3 className='uppercase lm__font--70 font-extrabold text-skyblue mb-2'>
        {name}
      </h3>
      <span className='uppercase lm__font--30'>{city}</span>
    </button>
  )
}
