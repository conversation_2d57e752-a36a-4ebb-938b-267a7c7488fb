import { ErrorBoundary as ReactErrorBoundary } from 'react-error-boundary'

import { Warning } from 'components/common/Warning'
import { Wave } from 'components/common/Wave'
import { DEV_ENV } from 'constants/configurations'
import useTranslation from 'hooks/useTranslation'

export const ErrorBoundary = ({ children, noFallback }) => (
  <ReactErrorBoundary FallbackComponent={noFallback ? NoFallback : Fallback}>
    {children}
  </ReactErrorBoundary>
)

const Fallback = ({ error }) => {
  const { t } = useTranslation()

  if (DEV_ENV) {
    return (
      <div
        className='flex items-center p-4 border-t-4 dark:text-red-400 bg-gray-800 border-red-800 w-full'
        role='alert'
      >
        <div className='ms-3 text-sm font-medium'>
          {'Error: '}
          {error?.toString()}
        </div>
      </div>
    )
  }

  return (
    <div className='flex flex-col items-center'>
      <h1 className='lm__title--page font-extrabold uppercase text-center text-silverChalice mt-10 mb-6'>
        {t('global.error.title')}
      </h1>

      <h2 className='lm__title--sub font-extrabold text-center w-4/5 mb-14'>
        {t('global.error.subtitle', { br: <br /> })}
      </h2>

      <Warning className='mb-14' />

      <Wave direction='up' peakCount={2} height={15} />
      <Wave direction='up' peakCount={3} height={0} color='lightSilver' />
    </div>
  )
}

const NoFallback = () => <></>
