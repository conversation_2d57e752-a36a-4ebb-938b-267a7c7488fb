import { Field, Form } from 'react-final-form'

import { ButtonIcon } from 'components/common/Button'
import { InputField } from 'components/common/form'
import { useCard } from 'hooks/useCard'
import useTranslation from 'hooks/useTranslation'
import { useNavigation } from 'hooks/useNavigation'
import { paths } from 'router/paths'

const CARD_FIELD = 'card'

export const CardQuery = ({ className = '' }) => {
  const { t } = useTranslation()
  const { setCardUid } = useCard()
  const { goTo } = useNavigation()

  const onSubmit = ({ [CARD_FIELD]: card }) => {
    setCardUid(card)
    goTo(paths.cardDetail(card))
  }

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <h3
        id='card-query-title'
        className='lm__font--50 font-light text-center w-3/4 mb-4'
      >
        {t('card.query.title')}
      </h3>

      <div className='w-4/5'>
        <Form
          onSubmit={onSubmit}
          render={({ handleSubmit, submitting }) => (
            <form
              onSubmit={handleSubmit}
              className='flex flex-col items-center '
            >
              <Field
                aria-describedby={'card-query-title'}
                component={InputField}
                name={CARD_FIELD}
                type='text'
                required
              />

              <ButtonIcon
                className='mt-4'
                type='submit'
                loading={submitting}
                disabled={submitting}
              />
            </form>
          )}
        />
      </div>
    </div>
  )
}
