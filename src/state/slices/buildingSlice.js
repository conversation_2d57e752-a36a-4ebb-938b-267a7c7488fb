import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  building: {},
  slug: undefined,
}

const buildingSlice = createSlice({
  name: 'building',
  initialState,
  reducers: {
    setSlug: (state, action) => {
      state.slug = action.payload
    },
  },
  extraReducers: (builder) => {
    // builder.addMatcher(logoutFulfilled, () => initialState)
  },
})

export const { setSlug } = buildingSlice.actions

export default buildingSlice.reducer
