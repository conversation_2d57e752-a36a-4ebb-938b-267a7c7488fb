import cn from 'classnames'
import { useCallback, useEffect, useMemo, useState } from 'react'

import { Dropdown } from 'components/common/Dropdown'
import { Icon } from 'components/common/Icon'
import { Spinner } from 'components/common/Spinner'
import { useBuilding } from 'hooks/useBuilding'
import { useTranslation } from 'hooks/useTranslation'
import { useUserCards } from 'hooks/useUserCards'
import { useDeleteUserCardMutation } from 'services/card'

const ADD_OPTION = {
  id: 'add',
  name: 'add',
  disabled: true,
}

export const CardSelector = ({ className = '', onSelected, onAdd }) => {
  const { t } = useTranslation()
  const { building } = useBuilding()
  const { buildingCards, isFetching } = useUserCards()
  const data = useMemo(
    () => [
      ...buildingCards.map((c) => ({
        id: c.uid,
        name: c.uid,
        card: c,
      })),
      ADD_OPTION,
    ],
    [buildingCards]
  )
  const [selected, setSelected] = useState(
    data.find(({ card }) =>
      card?.buildings?.some((b) => b.slug === building?.slug)
    )
  )

  const handleCardChange = useCallback(
    (option) => {
      if (option.id === ADD_OPTION.id) {
        onAdd?.()
        return
      }

      setSelected(option)
      onSelected?.(option.card)
    },
    [onAdd, onSelected]
  )

  useEffect(() => {
    handleCardChange(selected)
    // it's need to run only the first time
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  if (isFetching) return null

  return (
    <Dropdown
      className={`w-4/5 ${className}`}
      data={data}
      selected={selected}
      placeholder={t('cardDetail.selector.placeholder')}
      deactivateOnSingleOption
      onSelected={handleCardChange}
      OptionTemplate={Template}
      deactivateHighlighting={false}
    />
  )
}

const Template = (props) => {
  if (props.option?.id === ADD_OPTION.id) {
    return <Add {...props} />
  }

  return <Name {...props} />
}

const Name = ({
  selected = false,
  direction = 'down',
  placeholder = '',
  hideIcon = false,
  option,
  location,
  highlighted = false,
}) => {
  const [deleteCard, { isFetching }] = useDeleteUserCardMutation()

  const handleDelete = () => {
    deleteCard({ uid: option.id })
  }

  return (
    <div className='flex w-full items-center'>
      <div className='flex flex-1 min-w-0'>
        <div
          className={cn('flex flex-col min-w-0 w-full', {
            'p-3': highlighted,
          })}
        >
          <div className='relative flex items-center justify-center w-full'>
            <span className='lm__font--70 text-center font-bold uppercase'>
              {option?.name || placeholder}
            </span>
          </div>

          {location !== 'placeholder' && (
            <div className='w-full flex items-center justify-center gap-1'>
              <span className='min-w-0 truncate text-blue lm__font--30'>
                {option?.card?.buildings[0]?.name}
              </span>
              <span className='flex-none text-blue lm__font--30'>
                - {option?.card?.unit?.number}
              </span>
            </div>
          )}
        </div>

        {location === 'list' && highlighted ? (
          <button
            className='flex-none flex items-center justify-center bg-blue p-3'
            onClick={(e) => {
              e.stopPropagation()
              handleDelete()
            }}
          >
            {isFetching ? (
              <Spinner size={6} />
            ) : (
              <Icon kind='trash' className='w-6 h-6' color='white' />
            )}
          </button>
        ) : null}
      </div>

      {selected && !hideIcon ? (
        <Icon
          kind='chevron'
          className={cn('flex-none w-6 h-6', {
            'rotate-90': direction === 'down',
            '-rotate-90': direction === 'up',
          })}
          color='skyblue'
        />
      ) : null}
    </div>
  )
}

const Add = () => {
  const { t } = useTranslation()

  return (
    <div className='lm__font--40 text-center font-medium uppercase flex items-center justify-center w-full p-3'>
      <Icon kind='plus' className='w-8 h-8 mr-2' color='skyblue' />
      {t('cardDetail.selector.add.cta')}
    </div>
  )
}
