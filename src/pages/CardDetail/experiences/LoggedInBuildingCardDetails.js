import { useState } from 'react'

import { Card } from 'components/Card'
import { Conditional, ShowIf } from 'components/common/Conditional'
import { CARD } from 'constants/models/card'
import { useCard } from 'hooks/useCard'
import { useNavigation } from 'hooks/useNavigation'
import { useUserCards } from 'hooks/useUserCards'
import { paths } from 'router/paths'

import { CardSelector } from '../components/CardSelector'
import { Cost } from '../components/Cost'
import { NoCardsState } from '../components/NoCardsState'
import { Skeleton } from '../components/Skeleton'

export const LoggedInBuildingCardDetails = () => {
  const { isFetching: isUserCardsFetching, buildingCards } = useUserCards()
  const { goTo } = useNavigation()

  const [selectedUid, setSelectedUid] = useState()
  const { card, isFetching: isCardFetching } = useCard({ uid: selectedUid })

  if (isUserCardsFetching) return <Skeleton />
  if (buildingCards.length === 0) return <NoCardsState />

  return (
    <div className='flex flex-col items-center justify-center h-screen'>
      <CardSelector
        className='my-10'
        onAdd={() => {
          goTo(paths.cardRegistration)
        }}
        onSelected={({ uid }) => {
          if (!uid) return

          setSelectedUid(uid)
        }}
      />

      <Conditional>
        <ShowIf condition={isCardFetching || !card}>
          <Skeleton section='second' />
        </ShowIf>
        <ShowIf condition={card?.contractType === CARD.CONTRACT_TYPE.PREPAID}>
          <Card.Balance className='mb-8' card={card} />
        </ShowIf>
        <ShowIf condition={card?.contractType === CARD.CONTRACT_TYPE.POSTPAID}>
          <Cost className='mb-8' rate={card?.buildings?.[0].rate} />
        </ShowIf>
      </Conditional>

      {!isCardFetching && !!card && <Card.Activity items={card?.activity} />}
    </div>
  )
}

export default LoggedInBuildingCardDetails
