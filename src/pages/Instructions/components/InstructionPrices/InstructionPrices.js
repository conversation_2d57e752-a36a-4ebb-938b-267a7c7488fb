import { Prices } from 'components/Prices'
import { BUILDING } from 'constants/models/building'
import { useBuilding } from 'hooks/useBuilding'

export const InstructionPrices = () => {
  const { building } = useBuilding()

  return (
    <Prices
      filter={{
        // if it is a laundromat, dont show soap dispenser options
        soapDispenser:
          building?.type === BUILDING.TYPE.LAUNDROMAT ? false : undefined,
      }}
    />
  )
}
