import { uniqWith } from 'lodash'
import { useEffect, useMemo } from 'react'

import { BUILDING } from 'constants/models/building'

import { Container } from '../components/Container'
import { Filters } from '../components/Filters'
import { useMapBuilding } from '../hooks/useMapBuilding'
import { useStateMap } from '../hooks/useStateMap'

export const LoggedInUnknownMap = () => {
  const { laundromatCards, buildingCards, updateSlug } = useMapBuilding()
  const { step, advance, locationType, location } = useStateMap()

  const locationTypes = useMemo(() => {
    const options = []
    if (buildingCards?.length) options.push(BUILDING.TYPE.BUILDING)
    if (laundromatCards?.length) options.push(BUILDING.TYPE.LAUNDROMAT)
    return options
  }, [buildingCards, laundromatCards])

  const locations = useMemo(() => {
    let options = []
    if (locationType === BUILDING.TYPE.BUILDING)
      options = buildingCards?.flatMap(({ buildings }) => buildings)
    if (locationType === BUILDING.TYPE.LAUNDROMAT)
      options = laundromatCards?.flatMap(({ buildings }) => buildings)

    return uniqWith(options, (b1, b2) => b1.slug === b2.slug)
  }, [buildingCards, laundromatCards, locationType])

  useEffect(() => {
    if (!location) return

    updateSlug(location.slug)
  }, [location, updateSlug])

  return (
    <Container>
      <Filters
        mode={step}
        locationTypes={locationTypes}
        locations={locations}
        onSelect={advance}
      />
    </Container>
  )
}

export default LoggedInUnknownMap
