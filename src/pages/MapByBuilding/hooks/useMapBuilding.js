import { createContext, useCallback, useContext, useMemo } from 'react'
import { useParams } from 'react-router-dom'

import { ONE_MINUTE } from 'constants/time'
import { useBuilding } from 'hooks/useBuilding'
import { useMachine } from 'hooks/useMachine'
import { useUserCards } from 'hooks/useUserCards'
import { useExperience } from 'lib/experience'

const POLLING_INTERVAL = ONE_MINUTE * 2
export const MACHINES_GROUPED_HAS_SOAP_DISPENSER_KEYS = ['false', 'true']
export const MACHINES_GROUPED_RESERVED_KEYS = [
  'type',
  'set',
  ...MACHINES_GROUPED_HAS_SOAP_DISPENSER_KEYS,
]

const MapBuildingContext = createContext()

export const MapBuildingProvider = ({ children }) => {
  const { slug: paramSlug } = useParams()
  const {
    building,
    isFetching: isFetchingBuilding,
    isError: isErrorBuilding,
    updateSlug: updateBuildingSlug,
    slug,
  } = useBuilding({
    slug: paramSlug,
    level: 1,
  })
  const {
    machines,
    isFetching: isFetchingMachines,
    isError: isErrorMachines,
  } = useMachine({
    buildingSlug: slug,
    options: {
      pollingInterval: POLLING_INTERVAL,
      skipPollingIfUnfocused: true,
    },
  })

  const machinesGrouped = useMemo(
    () =>
      machines?.reduce(
        (acc, machine) => {
          /**
           * result => { type: { <type>: { set: [], <capacity>: [], <hasSoapDispenser:true|false>: [] } } }
           */
          // create and populate the type key
          if (!acc.type[machine.type]) {
            acc.type[machine.type] = { set: [] }
          }
          acc.type[machine.type].set.push(machine)
          // create and populate the capacity key
          if (!acc.type[machine.type][machine.capacity]) {
            acc.type[machine.type][machine.capacity] = []
          }
          acc.type[machine.type][machine.capacity].push(machine)
          // create and populate the hasSoapDispenser key
          const hasSoapDispenser = Boolean(machine.hasSoapDispenser).toString()
          if (!acc.type[machine.type][hasSoapDispenser]) {
            acc.type[machine.type][hasSoapDispenser] = []
          }
          acc.type[machine.type][hasSoapDispenser].push(machine)

          /**
           * result => { capacity: { <capacity>: { set: [], <type>: [] } } }
           */
          // create and populate the capacity key
          if (!acc.capacity[machine.capacity]) {
            acc.capacity[machine.capacity] = { set: [] }
          }
          acc.capacity[machine.capacity].set.push(machine)
          // create and populate the type key
          if (!acc.capacity[machine.capacity][machine.type]) {
            acc.capacity[machine.capacity][machine.type] = []
          }
          acc.capacity[machine.capacity][machine.type].push(machine)

          /*
           * :warning:
           * add reserved keywords to the MACHINES_GROUPED_RESERVED_KEYS
           */
          return acc
        },
        {
          type: {},
          capacity: {},
        }
      ),
    [machines]
  )

  const {
    buildingCards,
    laundromatCards,
    isFetching: isFetchingCards,
    isError: isErrorCards,
  } = useUserCards()

  const { isLoggedInBuilding, isLoggedInUnknown } = useExperience()
  const updateSlug = useCallback(
    (_slug) => {
      /**
       * The building only can be override when it is logged-in building or
       * logged-in unknown experience.
       * These are the only experiences that allows user to switch between
       * buildings or selection for the first time.
       * On xLM causes an infinite loop because overrides the scanned building.
       */
      if (!isLoggedInBuilding && !isLoggedInUnknown) return
      updateBuildingSlug(_slug)
    },
    [isLoggedInBuilding, isLoggedInUnknown, updateBuildingSlug]
  )

  return (
    <MapBuildingContext.Provider
      value={{
        building,
        machines,
        machinesGrouped,
        isFetching: isFetchingMachines || isFetchingBuilding || isFetchingCards,
        isError: isErrorBuilding || isErrorMachines || isErrorCards,
        updateSlug,
        slug,
        buildingCards,
        laundromatCards,
      }}
    >
      {children}
    </MapBuildingContext.Provider>
  )
}

export const useMapBuilding = () => {
  const context = useContext(MapBuildingContext)
  if (!context) {
    throw new Error('useMapBuilding must be used within a MapBuildingProvider')
  }
  return context
}
