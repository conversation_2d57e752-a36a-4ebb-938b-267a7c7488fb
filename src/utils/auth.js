import Cookie from 'cookie-universal'

import { AUTH_REMEMBER_USER_COOKIE, AUTH_USER_KEY } from 'constants/constants'
import { paths } from 'router/paths'

export const setLoggedInUser = (payload) =>
  localStorage.setItem(AUTH_USER_KEY, JSON.stringify(payload))

export const getLoggedInUser = () => localStorage.getItem(AUTH_USER_KEY)

export const cleanLoggedInUser = () => localStorage.removeItem(AUTH_USER_KEY)

const cookies = Cookie()
export const remindUser = ({ email } = {}) =>
  email && cookies.set(AUTH_REMEMBER_USER_COOKIE, email)

export const getRemindedUser = () => {
  const email = cookies.get(AUTH_REMEMBER_USER_COOKIE)
  return email ? decodeURI(email) : undefined
}

export const resetRemindedUser = () => cookies.remove(AUTH_REMEMBER_USER_COOKIE)

export const redirectToLogin = (page) => {
  window.location.href = paths.LOGIN
}
