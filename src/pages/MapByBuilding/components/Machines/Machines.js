import cn from 'classnames'
import { bool, number, oneOf, shape, string } from 'prop-types'
import { useEffect, useMemo } from 'react'

import { Conditional, ShowIf } from 'components/common/Conditional'
import { SkeletonLoader } from 'components/common/SkeletonLoader'
import { ACTIVATION_FEATURE_ENABLED } from 'constants/configurations'
import { BUILDING } from 'constants/models/building'
import { MACHINE } from 'constants/models/machine'
import { useActivation } from 'hooks/useActivation'
import { useNavigation } from 'hooks/useNavigation'
import { useTranslation } from 'hooks/useTranslation'
import { paths } from 'router/paths'

import { useMapBuilding } from '../../hooks/useMapBuilding'
import { ERROR, useStateMap } from '../../hooks/useStateMap'
import { Machine } from '../Machine'

import classes from './Machines.module.scss'

export const Machines = ({
  filter = {},
  hideTitle = false,
  className = 'w-4/5 mb-5',
  columns = 4,
}) => {
  const { t } = useTranslation()

  const {
    machinesGrouped,
    isFetching,
    isError,
    building,
    laundromatCards,
    buildingCards,
  } = useMapBuilding()
  const machines = useMemo(() => {
    const typeFiltered = machinesGrouped?.type?.[filter.type] || {}
    if ('capacity' in filter) return typeFiltered?.[filter.capacity] || []
    if ('hasSoapDispenser' in filter)
      return typeFiltered?.[filter.hasSoapDispenser] || []

    return typeFiltered?.set || []
  }, [machinesGrouped, filter])
  const isFiltering =
    ('capacity' in filter && filter.capacity === undefined) ||
    ('hasSoapDispenser' in filter && filter.hasSoapDispenser === undefined)

  const { goTo } = useNavigation()
  const { updateSelection } = useActivation()
  const { report } = useStateMap()

  useEffect(() => {
    if (isFetching || isError) return
    if (machines.length) return
    if (isFiltering) return

    report(ERROR.EMPTY)
  }, [machines, isFetching, isError, isFiltering, report])

  const handleAvailableSelected = (machine) => {
    const index = machine.index
    const slug = building.slug
    const uid =
      building.type === BUILDING.TYPE.LAUNDROMAT
        ? laundromatCards[0]?.uid
        : buildingCards[0]?.uid

    updateSelection({ slug, index, uid })
    if (ACTIVATION_FEATURE_ENABLED) {
      goTo(paths.activation(slug, index, uid))
    }
  }

  return (
    <div className={`flex flex-col ${className}`}>
      {!hideTitle && (
        <h2
          className={`${
            filter.type === MACHINE.TYPE.WASHER ? 'text-skyblue' : 'text-blue'
          } lm__font--60 font-normal uppercase`}
        >
          {t(`mapByBuilding.${filter.type}`)}
        </h2>
      )}
      <Conditional>
        <ShowIf condition={!!isFetching}>
          <Skeleton />
        </ShowIf>
        <ShowIf condition={!isFetching && (machines?.length || isFiltering)}>
          <div
            className={cn(
              classes['machines-container'],
              // eslint-disable-next-line local-rules/tailwind-dynamic-classes
              classes[`machines-container--${columns}`]
            )}
          >
            {machines.map((machine) => (
              <Machine
                key={machine.index}
                machine={machine}
                onAvailableSelected={handleAvailableSelected}
              />
            ))}
          </div>
        </ShowIf>
      </Conditional>
    </div>
  )
}

Machines.propTypes = {
  filter: shape({
    type: oneOf(Object.values(MACHINE.TYPE)).isRequired,
    capacity: number,
    hasSoapDispenser: oneOf(['true', 'false']),
  }).isRequired,
  hideTitle: bool,
  className: string,
  columns: oneOf([2, 3, 4, 5, 6]),
}

const Skeleton = () => (
  <>
    <SkeletonLoader className='h-20 mb-3' />
    <SkeletonLoader className='h-20' />
  </>
)
