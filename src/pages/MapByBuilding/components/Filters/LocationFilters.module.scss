.container {
  animation: detect-scroll;
  animation-fill-mode: none;
  animation-timeline: --scroll-timeline;
  overflow-y: auto;
  scroll-timeline: --scroll-timeline y;
}

.container::before,
.container::after {
  animation-fill-mode: both;
  animation-name: reveal;
  animation-timeline: --scroll-timeline;
  content: "";
  display: block;
  height: .75rem;
  left: 0;
  position: sticky;
  right: 0;
  visibility: var(--visibility-if-can-scroll, var(--visibility-if-cant-scroll));

  --visibility-if-can-scroll: var(--can-scroll) visible;
  --visibility-if-cant-scroll: hidden;
}

.container::before {
  background: radial-gradient(farthest-side at 50% 0, rgb(0 0 0 / 25%), rgb(0 0 0 / 0%));
  top: 0;
}

.container::after {
  background: radial-gradient(farthest-side at 50% 100%, rgb(0 0 0 / 25%), rgb(0 0 0 / 0%));
  bottom: 0;
}

@keyframes reveal {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes detect-scroll {
  from,
  to {
    --can-scroll: ;
  }
}
