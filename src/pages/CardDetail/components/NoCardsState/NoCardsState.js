import { ButtonNavHome } from 'components/ButtonNavHome'
import { Button } from 'components/common/Button'
import { Wave } from 'components/common/Wave'
import { useTranslation } from 'hooks/useTranslation'
import { hashes, paths } from 'router/paths'

export const NoCardsState = () => {
  const { t } = useTranslation()

  return (
    <div className='relative flex flex-col items-center h-screen'>
      <h2 className='lm__font--50 font-light text-center mb-6 mt-16'>
        {t('cardDetail.noCards.title')}
      </h2>

      <Button type='a' href={paths.cardRegistration} className='mb-32'>
        {t('cardDetail.noCards.cta')}
      </Button>

      <h3 className='lm__font--50 font-light text-center mb-4'>
        {t('cardDetail.noCards.subtitle')}
      </h3>

      <ButtonNavHome
        hash={hashes.virtualAssistant}
        kind='secondary'
        className='mb-8'
      >
        {t('cardDetail.noCards.request')}
      </ButtonNavHome>

      <Wave direction='up' peakCount={3} color='cornflower' height={10} />
      <Wave direction='up' peakCount={2} color='skyblue' height={5} />
    </div>
  )
}
