import { lazy } from 'react'

import { Home } from 'pages/Home'
import { EXPERIENCE } from 'lib/experience'

import { hashes, paths } from '../paths'

export const routes = [
  /**
   * How to handle main flow. Redirection from /<path> to #<section>
   */

  // {
  //   path: '/card',
  //   exact: true,
  //   redirectTo: paths.wallet,
  //   experiences: [EXPERIENCE.loggedIn.laundromat],
  // TODO: make redirection inside the components: Card and Wallet
  // },
  {
    path: '/card',
    exact: true,
    redirectTo: `/#${hashes.card}`,
  },
  {
    path: '/instructions',
    exact: true,
    redirectTo: `/#${hashes.instructions}`,
  },
  {
    path: '/map',
    exact: true,
    redirectTo: `/#${hashes.map}`,
  },
  {
    path: '/wallet',
    exact: true,
    redirectTo: `/#${hashes.wallet}`,
  },

  /**
   * Conditioned routes based on user's experience
   */

  {
    path: paths.cardRegistration,
    exact: true,
    component: lazy(() =>
      import(/* webpackChunkName: "mixed-exp" */ 'pages/CardRegistration')
    ),
    experiences: [EXPERIENCE.loggedIn.building],
  },
  {
    path: paths.cardError,
    exact: true,
    component: lazy(() =>
      import(/* webpackChunkName: "mixed-exp" */ 'pages/CardError')
    ),
    experiences: [
      EXPERIENCE.loggedIn.building,
      EXPERIENCE.loggedIn.unknown,
      EXPERIENCE.nonLoggedIn.building,
    ],
  },
  {
    path: paths.cardDetail(),
    exact: true,
    component: lazy(() =>
      import(/* webpackChunkName: "mixed-exp" */ 'pages/CardDetail')
    ),
    experiences: [
      EXPERIENCE.loggedIn.building,
      EXPERIENCE.loggedIn.unknown,
      EXPERIENCE.nonLoggedIn.building,
    ],
  },

  {
    path: paths.mapByBuilding(),
    exact: true,
    private: true,
    component: lazy(() =>
      import(/* webpackChunkName: "logged-in-exp" */ 'pages/MapByBuilding')
    ),
    experiences: [
      EXPERIENCE.loggedIn.building,
      EXPERIENCE.loggedIn.laundromat,
      EXPERIENCE.loggedIn.unknown,
    ],
  },
  {
    path: paths.activation(),
    exact: true,
    component: lazy(() =>
      import(/* webpackChunkName: "logged-in-exp" */ 'pages/Activation')
    ),
    experiences: [EXPERIENCE.loggedIn.building, EXPERIENCE.loggedIn.laundromat],
  },

  {
    path: paths.login,
    exact: true,
    component: lazy(() =>
      import(/* webpackChunkName: "non-logged-in-exp" */ 'pages/Login')
    ),
    experiences: [
      EXPERIENCE.nonLoggedIn.building,
      EXPERIENCE.nonLoggedIn.laundromat,
      EXPERIENCE.nonLoggedIn.unknown,
    ],
  },
  {
    path: paths.singUp,
    exact: true,
    component: lazy(() =>
      import(/* webpackChunkName: "non-logged-in-exp" */ 'pages/Signup')
    ),
    experiences: [
      EXPERIENCE.nonLoggedIn.building,
      EXPERIENCE.nonLoggedIn.laundromat,
      EXPERIENCE.nonLoggedIn.unknown,
    ],
  },

  /**
   * Place these two at the bottom to allow redirection to work properly
   */
  {
    path: paths.indexWithBuilding(),
    component: Home,
  },
  {
    path: paths.index,
    component: Home,
  },
]
