export const SoapBrands = (props) => (
  <svg
    viewBox='0 0 417 91'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path d='M112.564 86.1507H0V90.427H112.728C133.296 90.427 150.104 73.8395 150.104 53.575V52.7852C150.104 71.1158 133.214 86.1507 112.564 86.1507Z' />
    <path d='M37.3762 0C20.2137 0 5.63912 11.5758 1.30762 27.21C6.26568 13.9182 20.4043 4.27624 36.9403 4.27624H150.104V0H37.3762Z' />
    <path d='M80.8818 64.0889C81.1815 64.5247 81.4539 65.015 81.4539 65.4508C81.4539 66.5947 80.3642 66.5947 79.7649 66.5947H70.2029C68.8136 66.5947 68.6774 66.4585 67.9418 65.2873L57.944 49.2174V65.2056C57.944 66.513 57.2902 66.5675 56.2005 66.5675H48.0278C46.7747 66.5675 46.4205 66.1317 46.4205 64.906V16.6689C46.4205 15.5795 46.5568 15.062 48.0278 14.5444L56.146 11.5211C57.4264 11.1398 57.9712 11.6028 57.9712 12.9647V44.0151L68.6501 30.9685C69.7398 29.6611 70.0395 29.3887 71.6468 29.307H77.858C78.3756 29.307 79.4652 29.307 79.4652 30.3965C79.4652 31.0502 79.0294 31.5405 78.648 31.9763L67.9146 44.3692L80.8818 64.0889ZM34.4068 44.6415L30.4567 42.8439C28.1138 41.8361 25.2807 40.6104 25.2807 37.5054C25.2807 35.8439 26.3159 34.0463 28.4952 34.0463C30.6201 34.0463 31.7098 35.8439 32.4998 37.2058C33.5078 38.9217 34.543 40.6649 37.3217 40.6649C40.1549 40.6649 41.5442 38.5132 41.5442 36.3342C41.5442 33.0113 38.2479 28.272 28.9039 28.272C16.3452 28.272 15.2556 36.8517 15.2556 39.3575C15.2556 46.2758 20.6495 48.8633 23.292 50.089L26.4248 51.6143C30.157 53.4119 32.2002 54.4197 32.2002 57.3069C32.2002 59.5403 30.5111 60.8477 28.8494 60.8477C26.8879 60.8477 25.6348 59.1862 25.1172 58.4781C24.8993 58.124 23.7279 56.5442 23.5099 56.1629C21.9026 54.0111 20.2136 53.6298 19.1239 53.6298C16.2908 53.6298 14.52 55.8633 14.52 58.4508C14.52 61.8282 18.3067 67.6025 28.9039 67.6025C39.7735 67.6025 42.7156 60.766 42.7156 55.7816C42.7156 48.7271 37.24 46.0034 34.4068 44.6415ZM89.8173 25.3304C92.7322 25.3304 95.4291 23.7506 95.4291 20.2915C95.3474 17.6222 93.6856 15.2526 89.7355 15.2526C85.7309 15.2526 83.7423 17.5678 83.7423 20.3732C83.7695 23.5872 86.4665 25.3304 89.8173 25.3304ZM134.631 46.3575C134.631 56.2174 129.89 67.6025 118.421 67.6025C116.46 67.6025 114.553 67.2484 112.728 66.2951V68.0111V78.1705C112.728 79.2328 112.592 79.7503 111.121 80.295L103.057 83.2911C101.804 83.6725 101.232 83.2094 101.232 81.8476V73.5947V66.7309V36.4976C101.232 34.4004 101.232 33.2564 102.049 32.3303C103.357 30.6689 109.05 28.2992 116.732 28.2992C119.212 28.2992 125.205 28.4354 129.373 32.2486C132.369 34.9723 134.631 40.0929 134.631 46.3575ZM122.726 47.9373C122.726 44.7777 122.426 41.5365 121.037 38.7311C120.002 36.7155 118.258 34.8362 115.779 34.8362C112.782 34.8362 112.782 36.9879 112.701 38.7856V59.3224C114.39 60.194 115.833 60.2485 116.215 60.2485C121.99 60.2485 122.726 51.696 122.726 47.9373ZM95.7015 30.914C95.7015 29.4977 95.1295 29.0074 93.8218 29.416L85.4313 32.5482C83.933 33.093 83.7695 33.6377 83.7695 34.7544V40.0112V46.9295V64.8788C83.7695 65.9683 83.8512 66.622 85.2406 66.622H93.8491C95.1022 66.622 95.6743 66.3224 95.6743 64.8243V47.338C95.6743 47.338 95.7015 47.338 95.7015 47.3108V30.914Z' />
    <mask
      id='mask0_1785_1131'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='170'
      y='2'
      width='247'
      height='89'
    >
      <path d='M417 2.2793H170.977V90.1737H417V2.2793Z' fill='white' />
    </mask>
    <g mask='url(#mask0_1785_1131)'>
      <path d='M334.592 32.8665C323.042 33.8198 315.85 43.8158 316.803 55.3644C317.729 66.8857 326.447 75.5744 337.97 74.6211C349.521 73.6678 356.713 63.6717 355.759 52.1232C354.833 40.6018 346.116 31.9132 334.592 32.8665ZM337.453 68.3021C332.631 68.7106 329.307 62.2826 328.681 54.3838C328.027 46.4851 330.261 39.6213 335.11 39.2127C339.932 38.8042 343.255 45.2321 343.882 53.1309C344.508 61.0297 342.275 67.8935 337.453 68.3021Z' />
    </g>
    <mask
      id='mask1_1785_1131'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='170'
      y='2'
      width='247'
      height='89'
    >
      <path d='M417 2.2793H170.977V90.1737H417V2.2793Z' fill='white' />
    </mask>
    <g mask='url(#mask1_1785_1131)'>
      <path d='M221.838 24.5323C210.56 21.8903 200.643 29.2171 198.001 40.4661C195.358 51.7151 200.97 62.6644 212.249 65.3064C223.527 67.9484 233.443 60.6216 236.085 49.3727C238.728 38.1237 233.116 27.1743 221.838 24.5323ZM224.48 46.6489C222.655 54.3571 218.433 60.2131 213.72 59.1236C209.007 58.0341 207.835 50.8979 209.633 43.1898C211.459 35.4817 215.681 29.6257 220.394 30.7152C225.107 31.8319 226.278 38.9408 224.48 46.6489Z' />
    </g>
    <mask
      id='mask2_1785_1131'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='170'
      y='2'
      width='247'
      height='89'
    >
      <path d='M417 2.2793H170.977V90.1737H417V2.2793Z' fill='white' />
    </mask>
    <g mask='url(#mask2_1785_1131)'>
      <path d='M319.12 38.5864C319.937 37.3879 320.155 35.9171 319.746 34.3646C319.692 34.2012 319.583 34.0378 319.419 33.9833C319.256 33.9016 319.065 33.9016 318.902 33.9833C318.084 34.3646 315.96 34.8276 310.566 34.9366V29.2985C310.566 27.2012 310.566 25.0222 311.219 23.4152C311.928 21.6993 313.317 20.9094 315.605 20.8822C316.859 20.8822 319.338 21.209 322.007 23.4425C322.144 23.5514 322.334 23.6059 322.525 23.5787C322.716 23.5514 322.852 23.4152 322.934 23.2518C323.751 21.5631 323.778 19.711 322.961 18.0495C321.571 15.2169 318.193 13.4737 313.944 13.392C304.436 13.2285 299.015 19.1662 299.015 29.6253V35.1272H298.906C297.026 35.1545 295.719 35.672 295.038 36.6798C294.357 37.6875 294.329 39.1584 294.983 41.0105C295.092 41.2829 295.365 41.4735 295.664 41.419C296.727 41.2284 297.38 41.2284 298.824 41.2284H299.042L299.069 75.6834C299.069 79.4149 298.225 82.0569 296.563 83.3643C295.119 84.5083 293.349 84.3993 292.123 84.0997C291.932 84.0453 291.714 84.0997 291.551 84.2359C291.414 84.3721 291.333 84.59 291.387 84.7807C292.041 88.1581 294.547 90.1191 298.279 90.1736H298.552C302.665 90.1736 305.962 88.1581 308.087 84.3993C309.776 81.4033 310.675 77.3177 310.675 72.878L310.647 41.0377L314.407 40.9832C316.395 40.9288 318.112 40.0844 319.12 38.5864Z' />
    </g>
    <mask
      id='mask3_1785_1131'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='170'
      y='2'
      width='247'
      height='89'
    >
      <path d='M417 2.2793H170.977V90.1737H417V2.2793Z' fill='white' />
    </mask>
    <g mask='url(#mask3_1785_1131)'>
      <path d='M290.733 65.5246L292.041 50.9527C292.585 45.7232 291.55 41.6648 289.017 38.9684C286.619 36.4081 283.405 35.5909 281.144 35.3458C280.817 35.3186 280.49 35.2913 280.136 35.2913C276.894 35.2913 273.625 36.8983 271.119 39.7038C268.885 34.9372 264.254 33.6843 261.693 33.3575C261.284 33.303 260.876 33.2758 260.44 33.2758C257.852 33.2758 255.182 34.3108 252.921 36.1902C251.695 32.84 248.453 30.8789 244.231 30.8789C243.277 30.8789 242.269 30.9879 241.289 31.2058C241.098 31.233 240.934 31.3692 240.88 31.5598C240.798 31.7233 240.798 31.9412 240.907 32.1046C241.997 34.0657 242.079 37.1707 241.18 43.2174L237.829 64.762C237.42 67.4857 237.856 69.583 239.082 70.9993C240.553 72.7152 242.869 73.0693 244.558 73.0693C245.811 73.0693 247.2 72.8514 248.562 72.4701C248.807 72.3884 248.998 72.1705 248.998 71.9254C249.134 69.474 249.352 67.0771 250.278 60.8398L252.73 45.1512C253.438 43.0267 254.937 41.7738 256.708 41.7465C256.871 41.7465 257.034 41.7466 257.171 41.7738C259.105 42.0189 260.985 43.8166 260.63 46.622L257.906 67.8943C257.552 70.5907 258.015 72.6608 259.268 74.0771C260.739 75.7386 262.973 76.0654 264.581 76.0654C265.888 76.0654 267.359 75.8203 268.803 75.3845C269.048 75.3028 269.212 75.0849 269.239 74.8397C269.321 72.3884 269.511 69.9915 270.301 63.7542C270.301 63.7542 272.236 48.5286 272.263 48.2018V48.1745C272.889 44.9878 274.851 43.8711 276.458 43.8711C276.594 43.8711 276.703 43.871 276.839 43.8983C277.847 44.0072 278.801 44.4975 279.482 45.2874C280 45.9138 280.599 47.0033 280.435 48.6648L278.637 67.9487C278.338 70.9993 279.019 73.5324 280.626 75.33C282.179 77.046 284.522 77.972 287.273 77.972C288.417 77.972 289.589 77.8086 290.787 77.4818C290.978 77.4273 291.114 77.2911 291.196 77.1277C291.278 76.9642 291.251 76.7463 291.169 76.5829C290.025 74.6763 290.106 71.6258 290.733 65.5246Z' />
    </g>
    <mask
      id='mask4_1785_1131'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='170'
      y='2'
      width='247'
      height='89'
    >
      <path d='M417 2.2793H170.977V90.1737H417V2.2793Z' fill='white' />
    </mask>
    <g mask='url(#mask4_1785_1131)'>
      <path d='M375.347 24.0156C374.857 24.0156 374.339 24.0701 373.849 24.179C370.416 24.8872 367.992 27.039 366.63 30.5798C365.077 28.5098 362.843 27.4203 360.146 27.4203C358.021 27.4203 355.787 28.1284 353.662 29.4631C353.499 29.572 353.39 29.7354 353.39 29.9261C353.363 30.1168 353.444 30.3074 353.608 30.4164C355.297 31.8872 356.441 34.7743 357.694 40.7665L362.107 62.0932C363.143 67.1321 366.003 68.1944 368.182 68.1944C370.198 68.1944 372.568 67.2955 374.857 65.6613C375.075 65.4979 375.156 65.2527 375.075 65.0076L374.938 64.5446C374.257 62.3111 373.467 59.8053 372.323 54.1672L369.517 40.6031C369.054 38.0428 369.354 36.1907 370.444 34.7743C370.961 34.0934 371.642 33.7121 372.405 33.6576C374.257 33.5214 376.437 35.2918 377.309 37.6342C377.363 37.8249 377.526 37.9611 377.717 38.0155C377.908 38.07 378.099 38.0155 378.262 37.8794C381.477 35.1556 382.757 31.642 381.695 28.537C380.714 25.786 378.235 24.0156 375.347 24.0156Z' />
    </g>
    <mask
      id='mask5_1785_1131'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='170'
      y='2'
      width='247'
      height='89'
    >
      <path d='M417 2.2793H170.977V90.1737H417V2.2793Z' fill='white' />
    </mask>
    <g mask='url(#mask5_1785_1131)'>
      <path d='M415.529 44.3061C415.393 44.1972 415.175 44.1427 415.012 44.1972C414.821 44.2516 414.685 44.3606 414.603 44.5513C413.758 46.5123 412.533 47.7108 410.816 48.2555C410.299 48.4189 409.781 48.5006 409.318 48.5006C408.01 48.5006 405.586 47.847 404.196 43.3528L397.795 22.5164L401.472 21.2907C403.297 20.6642 404.632 19.3296 405.095 17.6681C405.504 16.2518 405.259 14.781 404.387 13.3919C404.305 13.2285 404.142 13.1468 403.951 13.1195C403.788 13.0923 403.597 13.1468 403.461 13.283C402.562 14.1001 400.056 15.244 395.969 16.6604L394.471 11.7577C393.327 8.02617 391.42 6.22852 388.641 6.22852C386.489 6.22852 383.956 7.39971 381.64 9.41527C381.449 9.57869 381.368 9.85106 381.477 10.0962C382.403 12.3569 383.22 14.5903 385.045 20.5553C383.492 21.1545 382.566 22.0261 382.267 23.0883C381.967 24.2595 382.403 25.6486 383.547 27.2012C383.738 27.4463 384.065 27.528 384.31 27.3646C385.127 26.9288 385.726 26.7109 386.789 26.3296L393.245 47.275C395.506 54.629 398.857 57.8975 404.088 57.8702C405.531 57.8702 407.166 57.5979 408.909 57.0531C414.576 55.3099 416.782 51.2244 416.946 48.2555C417.082 46.6213 416.537 45.1232 415.529 44.3061Z' />
    </g>
    <mask
      id='mask6_1785_1131'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='170'
      y='2'
      width='247'
      height='89'
    >
      <path d='M417 2.2793H170.977V90.1737H417V2.2793Z' fill='white' />
    </mask>
    <g mask='url(#mask6_1785_1131)'>
      <path d='M240.035 16.715C239.055 11.8396 232.189 6.66449 223.309 4.13143C218.977 2.87852 214.618 2.2793 210.341 2.2793C192.77 2.33377 178.059 13.2014 172.883 29.9795C169.642 40.4931 170.459 51.3063 175.226 60.4307C180.075 69.7458 188.793 76.7458 199.771 80.1232C202.822 81.0493 206.337 81.5668 209.688 81.5668C215.572 81.5396 220.257 79.9598 222.192 77.3178C223.227 75.9015 223.554 74.1855 223.118 72.2244C223.091 72.061 222.982 71.9248 222.818 71.8159C222.655 71.7342 222.491 71.7069 222.328 71.7886C219.794 72.7147 216.716 73.205 213.447 73.205C209.987 73.205 206.418 72.6875 203.149 71.6797C195.794 69.419 190.046 64.3801 186.995 57.5436C183.916 50.6526 183.671 42.1818 186.287 33.6838C189.637 22.8434 198.763 11.2403 214.319 11.1859C216.607 11.1859 218.895 11.4582 221.075 12.003C227.667 13.6372 233.688 18.6216 236.712 22.8434C236.821 22.9796 236.957 23.0613 237.148 23.0885C237.311 23.1158 237.502 23.0613 237.611 22.9251C239.708 21.1274 240.498 19.0302 240.035 16.715Z' />
    </g>
  </svg>
)

SoapBrands.toString = () => 'soapBrands'
