export const Coin = (props) => (
  <svg
    viewBox='0 0 41 41'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M19.8438 34.3191H20.6789C21.2357 34.3191 21.6731 33.8817 21.6731 33.4045V32.6091C21.6731 32.1717 22.031 31.774 22.5082 31.6945C24.2182 31.4161 25.5305 30.8594 26.5645 29.9845C27.7177 28.9903 28.3142 27.678 28.3142 26.0475C28.3142 24.4171 27.837 23.065 26.8429 22.0311C25.8487 20.9971 24.1785 20.0427 21.8322 19.0883C19.8438 18.3327 18.4122 17.6169 17.5373 17.0204C16.6624 16.4239 16.1852 15.5888 16.1852 14.5548C16.1852 13.5606 16.5829 12.7653 17.3385 12.2086C18.0941 11.6518 19.1678 11.3337 20.4801 11.3337C21.514 11.3337 22.3889 11.5723 23.1445 12.0097C23.7012 12.3279 24.1785 12.8051 24.6159 13.3618C24.8943 13.7197 25.4112 13.839 25.8487 13.6402L26.5247 13.322C27.0815 13.0834 27.2405 12.4472 26.9224 11.9699C26.4452 11.2939 25.8487 10.6974 25.1726 10.26C24.4171 9.74298 23.5422 9.42485 22.5878 9.22601C22.1106 9.14648 21.7924 8.7488 21.7924 8.31136V7.59555C21.7924 7.07858 21.355 6.68091 20.7982 6.68091H19.9631C19.4064 6.68091 18.9689 7.11835 18.9689 7.59555V8.35113C18.9689 8.78857 18.6508 9.18624 18.1736 9.26578C16.7818 9.54415 15.708 10.1009 14.8332 10.936C13.839 11.9302 13.322 13.1232 13.322 14.5548C13.322 16.1455 13.839 17.3783 14.8729 18.3327C15.9069 19.2473 17.418 20.1222 19.4859 20.9176C21.7924 21.8322 23.3433 22.6276 24.2182 23.3434C25.0931 24.0592 25.4908 24.9738 25.4908 26.0078C25.4908 27.0417 25.0533 27.8768 24.1387 28.4733C23.224 29.1096 22.1106 29.4278 20.7187 29.4278C19.3666 29.4278 18.2134 29.0699 17.1397 28.354C16.3443 27.7973 15.708 27.0815 15.2706 26.2464C15.0718 25.8089 14.5548 25.6101 14.0776 25.7294L13.4413 25.9282C12.8448 26.1271 12.5664 26.7634 12.8846 27.2803C13.4811 28.2745 14.1969 29.1096 14.9922 29.7459C15.8671 30.4617 16.9408 31.0184 18.1736 31.4161C18.5713 31.5354 18.8496 31.8933 18.8496 32.291V33.2852C18.8496 33.8817 19.2871 34.3191 19.8438 34.3191Z'
      strokeMiterlimit='10'
    />
    <path d='M39.4093 12.5267C38.3356 10.0213 36.8642 7.87391 35.0349 6.00485C33.2056 4.17556 31.0184 2.70417 28.5131 1.63046C26.0078 0.556741 23.3434 0 20.5199 0C17.7362 0 15.0718 0.556741 12.5664 1.63046C10.0611 2.70417 7.87391 4.17556 6.04462 6.00485C4.17556 7.83414 2.70417 10.0213 1.63046 12.5267C0.556741 14.9922 0 17.6566 0 20.5199C0 23.3036 0.556741 25.968 1.63046 28.4733C2.70417 30.9787 4.17556 33.1659 6.04462 34.9951C7.91368 36.8244 10.1009 38.3356 12.5664 39.4093C15.0718 40.483 17.6964 41.0398 20.5199 41.0398C23.3434 41.0398 26.0078 40.483 28.5131 39.4093C31.0184 38.3356 33.1659 36.8642 35.0349 34.9951C36.904 33.1261 38.3356 30.9389 39.4093 28.4733C40.483 25.968 41.0398 23.3434 41.0398 20.5199C41 17.6566 40.483 14.9922 39.4093 12.5267ZM31.8138 31.8138C28.7119 34.9156 24.934 36.5063 20.4801 36.5063C16.1057 36.4665 12.3278 34.9156 9.22599 31.8138C6.12415 28.7119 4.53346 24.934 4.53346 20.5199C4.53346 16.066 6.08438 12.2881 9.22599 9.18623C12.3278 6.08438 16.1057 4.53346 20.5199 4.53346C24.9738 4.53346 28.7517 6.08438 31.8535 9.18623C34.9554 12.2881 36.5063 16.066 36.5063 20.5199C36.4665 24.934 34.9156 28.6722 31.8138 31.8138Z' />
  </svg>
)

Coin.toString = () => 'coin'
