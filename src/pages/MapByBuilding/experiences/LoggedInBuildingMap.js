import cn from 'classnames'
import { useMemo } from 'react'

import { Prices } from 'components/Prices'
import { MACHINE } from 'constants/models/machine'

import { Container } from '../components/Container'
import { Header } from '../components/Header'
import { Machines } from '../components/Machines'
import {
  MACHINES_GROUPED_HAS_SOAP_DISPENSER_KEYS,
  useMapBuilding,
} from '../hooks/useMapBuilding'

const placeholder = {
  [MACHINES_GROUPED_HAS_SOAP_DISPENSER_KEYS[0]]: [], // placeholder
}

export const LoggedInBuildingMap = ({ location }) => {
  const { machinesGrouped } = useMapBuilding()
  const soapWashersOptions = useMemo(
    () =>
      Object.keys(machinesGrouped?.type?.[MACHINE.TYPE.WASHER] || placeholder)
        .filter((x) => MACHINES_GROUPED_HAS_SOAP_DISPENSER_KEYS.includes(x))
        .sort(), // false first
    [machinesGrouped]
  )

  return (
    <Container>
      <Header location={location} />

      <div className='flex flex-col items-start px-4 w-full justify-around'>
        {soapWashersOptions.map((hasSoapDispenser, index) => (
          <div
            key={`washer-list-${index}`}
            className={cn('flex w-full items-center p-4', {
              'bg-cornflower/40 rounded-md': index % 2 === 1,
            })}
          >
            <Machines
              filter={{ type: MACHINE.TYPE.WASHER, hasSoapDispenser }}
              className='w-3/5'
              columns={3}
              hideTitle={index > 0}
            />
            <Prices
              filter={{ soapDispenser: hasSoapDispenser === 'true' }}
              className='w-2/5 ml-6'
            />
          </div>
        ))}

        <div className='flex w-full items-center px-4 mt-8'>
          <Machines
            filter={{ type: MACHINE.TYPE.DRYER }}
            className='w-3/5'
            columns={3}
          />
          <Prices filter={{ soapDispenser: false }} className='w-2/5 ml-6' />
        </div>
      </div>
    </Container>
  )
}

export default LoggedInBuildingMap
