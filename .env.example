# Development environment
## Controls whether React Fast Refresh is enabled (optional, defaults to true)
FAST_REFRESH=false
## ESLint no dev errors
ESLINT_NO_DEV_ERRORS=true
## Skip Redux logger
NO_REDUX_LOGGER=true

# Application
## API URL for backend services
REACT_APP_API_URL=http://localhost:9000
## Public site URL
REACT_APP_PUBLIC_SITE_URL=http://localhost:8080
## Google reCAPTCHA site key
REACT_APP_RECAPTCHA_KEY=
## Survey links for feedback (optional)
REACT_APP_SURVEY_LINK_BUILDING=https://example.com/survey-test-link-building
REACT_APP_SURVEY_LINK_LAUNDROMAT=https://example.com/survey-test-link-laundromat
## Flag to enable/disable the activation feature (optional, defaults to false)
REACT_APP_ACTIVATION_FEATURE_ENABLED=true
