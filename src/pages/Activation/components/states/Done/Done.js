import { ButtonNavHome } from 'components/ButtonNavHome'
import Icon from 'components/common/Icon'
import { PageTitle } from 'components/common/PageTitle'
import { useTranslation } from 'hooks/useTranslation'
import { FormattedMessage } from 'react-intl'
import { hashes } from 'router/paths'

import { useStateMachine } from '../../../contexts/StateMachineContext'

export const Done = () => {
  const { t } = useTranslation()
  const {
    data: { index, cycleTime },
  } = useStateMachine()

  return (
    <div className='relative flex flex-col items-center text-center h-screen'>
      <PageTitle
        className='my-10'
        title={t('activation.title')}
        subtitle={t('activation.subtitle')}
      />

      <h2 className='lm__font--70 uppercase text-skyblue font-extrabold flex flex-col items-center mb-4'>
        <FormattedMessage
          id='activation.done.machineOn'
          values={{
            i: (chunks) => (
              <span className='flex items-center justify-center lm__font--80 text-white font-extrabold rounded-full bg-blue w-24 h-24 my-4'>
                {chunks}
              </span>
            ),
            index,
          }}
        />
      </h2>

      <p className='lm__font--50 uppercase text-skyblue mb-8'>
        <FormattedMessage
          id='activation.done.remainingTime'
          values={{
            s: (chunks) => (
              <span className='block lm__font--40 text-blue'>{chunks}</span>
            ),
            cycleTime,
          }}
        />
      </p>

      <ButtonNavHome hash={hashes.map} className='mb-8'>
        {t('activation.done.cta')}
      </ButtonNavHome>

      <Icon kind='clock' color='blue' className='w-16 h-16 mb-4' />

      <p className='lm__font--40 w-3/4'>{t('activation.done.reminder')}</p>
      <p className='w-3/4'>{t('activation.done.disclaimer')}</p>
    </div>
  )
}
