import { useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import {
  selectIndex,
  selectSlug,
  selectUid,
  useActivateMutation,
} from 'services/activation'
import { setIndex, setSlug, setUid } from 'state/slices/activationSlice'

export const useActivation = () => {
  const dispatch = useDispatch()
  const slug = useSelector(selectSlug)
  const index = useSelector(selectIndex)
  const uid = useSelector(selectUid)

  const [activate, result] = useActivateMutation()

  const updateSelection = useCallback(
    (selection = {}) => {
      if (selection.slug) dispatch(setSlug(selection.slug))
      if (selection.index) dispatch(setIndex(selection.index))
      if (selection.uid) dispatch(setUid(selection.uid))
    },
    [dispatch]
  )

  const trigger = () => {
    activate({ slug, index, uid })
  }

  return {
    updateSelection,
    slug,
    index,
    uid,
    trigger,
    ...result,
  }
}
