import { combineReducers } from '@reduxjs/toolkit'
import { persistReducer } from 'redux-persist'
import storage from 'redux-persist/lib/storage'

import 'services/auth'
import { api } from 'services/base'

import activation from './slices/activationSlice'
import auth from './slices/authSlice'
import building from './slices/buildingSlice'
import card from './slices/cardSlice'

const rootPersistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth'],
}

const rootReducer = combineReducers({
  [api.reducerPath]: api.reducer,
  activation,
  auth,
  building,
  card,
})

export default persistReducer(rootPersistConfig, rootReducer)
