import { useEffect, useRef } from 'react'

import Icon from 'components/common/Icon'
import { PageTitle } from 'components/common/PageTitle'
import { Wave } from 'components/common/Wave'
import { ONE_SECOND } from 'constants/time'
import { useBuilding } from 'hooks/useBuilding'
import { useTranslation } from 'hooks/useTranslation'

import { useStateMachine } from '../../../contexts/StateMachineContext'

export const Success = () => {
  const { t } = useTranslation()
  const { building } = useBuilding()
  const timer = useRef(null)
  const {
    next,
    data: { index },
  } = useStateMachine()

  useEffect(() => {
    timer.current = setTimeout(next, ONE_SECOND * 10)

    return () => {
      if (timer.current) clearTimeout(timer.current)
    }
  }, [next])

  return (
    <div className='relative flex flex-col items-center text-center h-screen'>
      <PageTitle
        className='my-10'
        title={t('activation.title')}
        subtitle={t('activation.subtitle')}
      />

      <div role='alert' className='flex items-center justify-center mb-20'>
        <div className='rounded-full bg-blue p-4 mr-4'>
          <Icon kind='check' color='skyblue' className='w-8 h-8' />
        </div>
        <span className='lm__font--40 uppercase'>
          {t('activation.success.state')}
        </span>
      </div>

      <ul className='lm__font--50 text-skyblue font-extrabold w-5/6'>
        <li>
          <div className='flex flex-col items-center justify-center'>
            <span>
              <Disc /> {t('activation.success.instructions.next')}
            </span>
            <span className='flex items-center justify-center lm__font--80 text-white font-extrabold rounded-full bg-skyblue w-24 h-24 my-4'>
              {index}
            </span>
          </div>
        </li>
        <li>
          <span>
            <Disc />
            {t('activation.success.instructions.cloth')}
          </span>
        </li>
        {!building?.hasSoapDispensers ? (
          <li>
            <span>
              <Disc />
              {t('activation.success.instructions.soap')}
            </span>
          </li>
        ) : null}
        <li>
          <span>
            <Disc />
            {t('activation.success.instructions.start')}
          </span>
        </li>
      </ul>

      <Wave direction='up' peakCount={3} />
      <Wave direction='up' peakCount={2} color='skyblue' />
    </div>
  )
}

const Disc = () => <span className='mr-2'>&#x2022;</span>
