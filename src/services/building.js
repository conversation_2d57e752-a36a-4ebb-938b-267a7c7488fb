import { DEFAULT_QUERY_LEVEL } from 'constants/constants'
import { buildEndpoint, endpoints } from 'services/endpoints'

import { injectEndpoints } from './base'

export const buildingApi = injectEndpoints({
  endpoints: (builder) => ({
    getBuilding: builder.query({
      query: ({ slug, level = DEFAULT_QUERY_LEVEL }) => ({
        url: buildEndpoint(endpoints.GET_BUILDING, { slug }, { level }),
        method: 'GET',
      }),
    }),
    getLaundromats: builder.query({
      query: (level = DEFAULT_QUERY_LEVEL) => ({
        url: buildEndpoint(endpoints.GET_LAUNDROMATS, {}, { level }),
        method: 'GET',
      }),
    }),
    getPricing: builder.query({
      query: ({ slug, level = DEFAULT_QUERY_LEVEL }) => ({
        url: buildEndpoint(endpoints.GET_PRICING, { slug }, { level }),
        method: 'GET',
      }),
    }),
  }),
})

export const {
  useGetBuildingQuery,
  useGetLaundromatsQuery,
  useGetPricingQuery,
} = buildingApi

// export const selectBuilding = (state) => state.building
export const selectSlug = (state) => state.building.slug
