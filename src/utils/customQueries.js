import { fetchBaseQuery } from '@reduxjs/toolkit/dist/query/react'
import camelcaseKeys from 'camelcase-keys'

import { selectAuth } from 'services/auth'
import { ACCESS_TOKEN, APPLICATION_JSON, CONTENT_TYPE } from 'constants/headers'
import { logout } from 'state/slices/authSlice'
import { endpoints } from 'services/endpoints'

const baseQuery = fetchBaseQuery({
  baseUrl: process.env.REACT_APP_API_URL,
  prepareHeaders: (headers, { getState }) => {
    const { token } = selectAuth(getState())

    if (token) {
      headers.set(CONTENT_TYPE, APPLICATION_JSON)
      headers.set(ACCESS_TOKEN, token)
    }
    return headers
  },
})

export const baseQueryWithReauth = async (args, api, extraOptions) => {
  const result = await baseQuery(args, api, extraOptions)
  const headers = result?.meta?.response?.headers
  const token = headers?.get(ACCESS_TOKEN)

  if (result.data) {
    result.data = camelcaseKeys(result.data, { deep: true })
  } else if (result.error?.status === 401) {
    if (args.url !== endpoints.SIGN_IN) {
      api.dispatch(logout({ forceLogin: true }))
    }
  } else if (result.error?.data) {
    result.error.data = camelcaseKeys(result.error.data, { deep: true })
  }

  if (token) {
    const session = {
      token,
    }

    result.data.info = session
  }

  return result
}
