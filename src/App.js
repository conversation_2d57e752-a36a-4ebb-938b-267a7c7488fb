import { Suspense, useEffect } from 'react'
import { Helmet } from 'react-helmet-async'
import { useDispatch } from 'react-redux'
import { BrowserRouter } from 'react-router-dom'

import { AppLoader } from 'components/AppLoader'
import { RecaptchaProvider } from 'hooks/useRecaptcha'
import useTranslation from 'hooks/useTranslation'
import { ExperienceProvider } from 'lib/experience'
import { Routes } from 'router/Routes'
import { setUser } from 'state/slices/authSlice'
import { getLoggedInUser } from 'utils/auth'

function App() {
  const { t } = useTranslation()
  const dispatch = useDispatch()

  useEffect(() => {
    const loggedInUser = getLoggedInUser()

    if (loggedInUser) {
      const user = JSON.parse(loggedInUser)
      dispatch(setUser(user))
    }
  }, [dispatch])

  return (
    <>
      <Helmet>
        <title>{t('global.pageTitle')}</title>
      </Helmet>
      <Suspense fallback={<AppLoader />}>
        <RecaptchaProvider>
          <ExperienceProvider>
            <BrowserRouter>
              <Routes />
            </BrowserRouter>
          </ExperienceProvider>
        </RecaptchaProvider>
      </Suspense>
    </>
  )
}

export default App
