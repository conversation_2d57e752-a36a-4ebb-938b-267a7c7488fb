import { ButtonIcon } from 'components/common/Button'
import { PageTitle } from 'components/common/PageTitle'
import { Warning } from 'components/common/Warning'
import { Wave } from 'components/common/Wave'
import { useNavigation } from 'hooks/useNavigation'
import { useTranslation } from 'hooks/useTranslation'

export const GenericError = () => {
  const { t } = useTranslation()
  const { goBack } = useNavigation()

  return (
    <div className='relative flex flex-col items-center text-center h-screen'>
      <PageTitle
        className='my-10'
        title={t('activation.title')}
        subtitle={t('activation.subtitle')}
      />

      <h2 className='lm__title--sub font-extrabold text-center w-4/5 mb-14'>
        {t('activation.error.generic')}
      </h2>

      <Warning className='mb-14' />

      <ButtonIcon icon='back' onClick={goBack} />

      <Wave direction='up' peakCount={3} height={15} />
      <Wave direction='up' peakCount={2} color='skyblue' height={10} />
    </div>
  )
}
