import cn from 'classnames'

import { basePropTypes, classes, kinds, types } from './base'

export const Button = ({
  children,
  className = '',
  kind = kinds.PRIMARY,
  type = types.BUTTON,
  ...props
}) => {
  const buttonClasses = cn(classes.BASE, 'lm__font--50', className, {
    [classes.PRIMARY]: kind === kinds.PRIMARY,
    'px-20': kind === kinds.PRIMARY,
    [classes.SECONDARY]: kind === kinds.SECONDARY,
    'px-10': kind === kinds.SECONDARY,
  })
  const Element = type === types.ANCHOR ? 'a' : 'button'

  return (
    <Element className={buttonClasses} type={type} {...props}>
      {children}
    </Element>
  )
}

Button.propTypes = {
  ...basePropTypes,
}
