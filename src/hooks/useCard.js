import { useCallback, useEffect } from 'react'

import { useDispatch, useSelector } from 'react-redux'

import { selectUid, useGetCardQuery } from 'services/card'
import { setUid } from 'state/slices/cardSlice'

import { useRecaptcha } from './useRecaptcha'

export const useCard = ({ uid } = {}) => {
  const dispatch = useDispatch()

  const {
    getToken,
    isReady: isRecaptchaReady,
    isSkippable: isRecaptchaSkippable,
    token: recaptchaToken,
    isFetching: isRecaptchaFetching,
  } = useRecaptcha()

  const cardUid = useSelector(selectUid) || uid

  const {
    data: card,
    isFetching,
    ...rest
  } = useGetCardQuery(
    { uid: cardUid, level: 2, recaptchaToken },
    {
      skip: !cardUid || !(isRecaptchaSkippable || recaptchaToken),
    }
  )

  const setCardUid = useCallback((uid) => dispatch(setUid(uid)), [dispatch])

  useEffect(() => {
    if (uid) {
      setCardUid(uid)
    }
  }, [cardUid, uid, setCardUid])

  useEffect(() => {
    if (cardUid && isRecaptchaReady) {
      getToken()
    }
  }, [cardUid, isRecaptchaReady, getToken])

  return {
    setCardUid,
    card,
    ...rest,
    isFetching: !isRecaptchaReady || isRecaptchaFetching || isFetching,
  }
}
