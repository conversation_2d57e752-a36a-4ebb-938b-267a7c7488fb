import { ErrorMessage } from 'components/ErrorMessage'
import { httpStatusCode } from 'constants/httpStatusCode'

import { useMapBuilding } from '../../hooks/useMapBuilding'
import { ERROR, STEP, useStateMap } from '../../hooks/useStateMap'
import { BadRequestError } from './BadRequestError'
import { EmptyError } from './EmptyError'

export const MapError = ({ ...props }) => {
  const { error, step } = useStateMap()
  const { isError } = useMapBuilding()

  if (!error && !isError) return null

  return (
    <Background>
      <ErrorMessage
        appError={error}
        defaultError={httpStatusCode.BAD_REQUEST}
        mapping={{
          [httpStatusCode.BAD_REQUEST]: BadRequestError,
          [ERROR.EMPTY]: EmptyError,
        }}
        backButton={step !== STEP.TYPE}
        {...props}
      />
    </Background>
  )
}

const Background = ({ children }) => (
  <div className='h-full'>
    <div className='absolute z-50 inset-0 bg-blue/90 flex items-center justify-center'>
      <div className='text-white text-6xl'>{children}</div>
    </div>
  </div>
)
