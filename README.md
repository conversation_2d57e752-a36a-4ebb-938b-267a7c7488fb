# Getting Started

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

## How to run

1. `nvm use $(cat .nvmrc)`
1. `yarn install` (don't use `npm`)
1. `yarn start`

## Which techs are involved

- React
- Redux
- [Tailwind](https://tailwindcss.com/docs) - for styles

## Resources

- :art: [figma](https://www.figma.com/file/JfydnAgWWVb4OOsl11hYlv/LAVOMAT_LANDING)
- [tailwind](https://tailwindcss.com/docs/preflight)

# How to deploy

## Sandbox Deployment

:information_source: Sandbox deployment is done automatically when a PR is merged into the `main` branch.

To deploy to sandbox manually:

1. Go to the [Sandbox Deploy Action](https://github.com/LAVOMAT/lm-assistant/actions/workflows/sandbox.yml)
1. Click on "Run workflow" dropdown
1. Optional: In the "Release tag to deploy (optional)" field, enter the version tag you want to deploy if is not the latest one, eg `v2.1`
1. Click "Run workflow" button

## Production Deployment

To deploy to production:

1. Go to the [Production Deploy Action](https://github.com/LAVOMAT/lm-assistant/actions/workflows/production.yml)
1. Click on "Run workflow" dropdown
1. Click "Run workflow" button

## Rollback

To perform a rollback:

1. Go to the [Production Deploy Action](https://github.com/LAVOMAT/lm-assistant/actions/workflows/production.yml)
1. Click on "Run workflow" dropdown
1. In the "Release tag to deploy (optional)" field, enter the version tag you want to revert to, eg `v2.1`
1. Click "Run workflow"
