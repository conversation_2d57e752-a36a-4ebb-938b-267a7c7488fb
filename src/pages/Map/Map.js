import { Layout } from 'components/common/Layout'
import { useExperience } from 'lib/experience'
import { LOCATION, MapByBuilding } from 'pages/MapByBuilding'
import { hashes } from 'router/paths'

export const Map = () => {
  const { isActivationEnabled } = useExperience()

  if (!isActivationEnabled) return null

  return (
    <Layout.Section id={hashes.map} className='h-screen'>
      <MapByBuilding location={LOCATION.homepage} />
    </Layout.Section>
  )
}
