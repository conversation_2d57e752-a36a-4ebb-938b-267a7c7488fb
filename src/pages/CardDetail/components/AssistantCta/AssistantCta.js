import { ButtonNavHome } from 'components/ButtonNavHome'
import useTranslation from 'hooks/useTranslation'
import { hashes } from 'router/paths'

export const AssistantCta = () => {
  const { t } = useTranslation()

  return (
    <div className='flex justify-center'>
      <ButtonNavHome hash={hashes.virtualAssistant} kind='secondary'>
        {t('cardDetail.assistant.cta')}
      </ButtonNavHome>
    </div>
  )
}
