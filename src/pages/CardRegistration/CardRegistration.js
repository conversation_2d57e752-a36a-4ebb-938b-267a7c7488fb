import { PageTitle } from 'components/common/PageTitle'
import { useTranslation } from 'hooks/useTranslation'

import cardTemplate from 'assets/card_template.jpg'

import { Form } from './components/Form'

export const CardRegistration = () => {
  const { t } = useTranslation()

  return (
    <div className='flex flex-col items-center h-screen'>
      <PageTitle
        className='my-10'
        title={t('cardRegistration.title')}
        subtitle={t('cardRegistration.subtitle')}
      />

      <img src={cardTemplate} alt='' className='w-1/2 mb-10' />

      <Form />
    </div>
  )
}
