import { Card } from 'components/Card'
import { SectionTitle } from 'components/common/SectionTitle'
import { useGetActivityQuery } from 'services/card'

import { useWallet } from '../../hooks/useWallet'
import { Skeleton } from '../Skeleton'

export const PageContent = () => {
  const { card, isFetching, isError } = useWallet()

  const { data: activity, isFetching: isActivityFetching } =
    useGetActivityQuery(
      {
        uid: card?.uid,
      },
      { skip: !card?.uid }
    )

  if (isError) return null
  if (isFetching || !card || isActivityFetching) return <Skeleton />

  return (
    <div className='flex flex-col items-center justify-center h-screen'>
      <SectionTitle
        className='mt-10 mb-4'
        intlId='wallet.title'
        values={{ uid: card?.uid }}
      />

      <Card.Balance className='mb-8' card={card} />

      <Card.Activity items={activity} />
    </div>
  )
}
