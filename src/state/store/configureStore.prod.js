import { configureStore } from '@reduxjs/toolkit'
import { persistStore } from 'redux-persist'

import reducer from 'state/reducer'
import { api } from 'services/base'

const middleware = (getDefaultMiddleware) =>
  getDefaultMiddleware({
    serializableCheck: {
      ignoredActions: ['persist/PERSIST'],
    },
  }).concat(api.middleware)

export const store = configureStore({
  reducer,
  middleware,
})

export const persistor = persistStore(store)
