import React, { useMemo } from 'react'

export const Conditional = ({ children }) => {
  const childrenList = useMemo(
    () => React.Children.toArray(children),
    [children]
  )

  let child = childrenList.find(
    (c) => React.isValidElement(c) && c.props.condition
  )
  if (child) return child

  child = childrenList.find((c) => React.isValidElement(c) && c.props.isDefault)
  if (child) return child

  return <></>
}
