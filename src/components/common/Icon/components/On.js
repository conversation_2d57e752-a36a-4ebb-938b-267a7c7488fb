export const On = (props) => (
  <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 157 179' {...props}>
    <path d='M150.876 69.0357C146.84 59.432 141.133 51.0809 134.035 43.9825C132.364 42.3123 130.694 40.7813 129.024 39.3894C125.684 36.6057 120.673 37.8584 119.003 41.8947C117.889 44.5392 118.585 47.4621 120.673 49.2715C122.065 50.5242 123.457 51.7768 124.848 53.1687C137.514 65.8344 143.917 81.2839 143.917 99.5171C143.917 117.75 137.514 133.2 124.848 145.865C112.183 158.531 96.7332 164.934 78.5 164.934C60.2668 164.934 44.8174 158.531 32.1516 145.865C19.4858 133.2 13.0833 117.75 13.0833 99.5171C13.0833 81.2839 19.4858 65.8344 32.1516 53.1687C34.6569 50.6634 37.1622 48.4364 39.8067 46.4878C42.3121 44.5392 43.1472 41.1988 41.6161 38.2759C39.8067 34.7963 35.2136 33.822 32.0124 36.1882C28.8112 38.5543 25.7491 41.1988 22.9654 43.9825C15.867 51.0809 10.2996 59.432 6.12411 69.0357C2.08777 78.6394 0 88.7999 0 99.5171C0 110.513 2.08777 120.673 6.2633 130.277C10.4388 139.881 16.0062 148.092 23.1046 155.191C30.203 162.289 38.4149 167.857 48.0186 171.893C57.6223 175.929 67.7828 178.017 78.5 178.017C89.2172 178.017 99.5168 175.929 108.981 171.893C118.585 167.857 126.936 162.289 134.035 155.191C141.133 148.092 146.84 139.881 150.876 130.277C154.912 120.673 157 110.513 157 99.5171C157 88.7999 154.912 78.6394 150.876 69.0357Z' />
    <path d='M78.9175 94.367H78.2216C74.742 94.367 71.8191 91.5833 71.8191 87.9645V6.40248C71.8191 2.92287 74.6028 0 78.2216 0H78.9175C82.3971 0 85.32 2.78369 85.32 6.40248V88.1037C85.1808 91.4441 82.3971 94.367 78.9175 94.367Z' />
  </svg>
)

On.toString = () => 'on'
