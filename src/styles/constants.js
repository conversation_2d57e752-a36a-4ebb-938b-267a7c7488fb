export const COLOR = {
  black: 'black',
  blue: 'blue',
  cornflower: 'cornflower',
  lightSilver: 'lightSilver',
  silverChalice: 'silverChalice',
  skyblue: 'skyblue',
  white: 'white',
}

/**
 * https://tailwindcss.com/docs/content-configuration#dynamic-class-names
 *
 * These classes definitions mitigate the limitation of Tailwind that all
 * classes have to be defined explicitly and can not be created dynamically in
 * order to be processed effectively.
 *
 */
export const CLASSES = {
  bg: (color) => ({
    'bg-black': color === COLOR.black,
    'bg-blue': color === COLOR.blue,
    'bg-cornflower': color === COLOR.cornflower,
    'bg-lightSilver': color === COLOR.lightSilver,
    'bg-silverChalice': color === COLOR.silverChalice,
    'bg-skyblue': color === COLOR.skyblue,
    'bg-white': color === COLOR.white,
  }),
  fill: (color) => ({
    'fill-black': color === COLOR.black,
    'fill-blue': color === COLOR.blue,
    'fill-cornflower': color === COLOR.cornflower,
    'fill-lightSilver': color === COLOR.lightSilver,
    'fill-silverChalice': color === COLOR.silverChalice,
    'fill-skyblue': color === COLOR.skyblue,
    'fill-white': color === COLOR.white,
  }),
  stroke: (color) => ({
    'stroke-black': color === COLOR.black,
    'stroke-blue': color === COLOR.blue,
    'stroke-cornflower': color === COLOR.cornflower,
    'stroke-lightSilver': color === COLOR.lightSilver,
    'stroke-silverChalice': color === COLOR.silverChalice,
    'stroke-skyblue': color === COLOR.skyblue,
    'stroke-white': color === COLOR.white,
  }),
  text: (color) => ({
    'text-black': color === COLOR.black,
    'text-blue': color === COLOR.blue,
    'text-cornflower': color === COLOR.cornflower,
    'text-lightSilver': color === COLOR.lightSilver,
    'text-silverChalice': color === COLOR.silverChalice,
    'text-skyblue': color === COLOR.skyblue,
    'text-white': color === COLOR.white,
  }),
}
