import { useMemo } from 'react'

import { SectionTitle } from 'components/common/SectionTitle'
import { useBuilding } from 'hooks/useBuilding'
import { useExperience } from 'lib/experience'

import { Instruction } from '../Instruction'

export const Washing = () => (
  <div className='flex flex-col items-center'>
    <SectionTitle intlId={'instructions.washing.title'} className='my-10' />
    <Instructions />
  </div>
)
const REPLACEABLE = 'soap'
const ALL_INSTRUCTIONS = ['on', 'cloth', REPLACEABLE, 'start', 'clock']
const LAUNDROMAT_INSTRUCTIONS = ['cloth', 'on', REPLACEABLE, 'start', 'clock']
const Instructions = () => {
  const { building } = useBuilding()
  const { isLaundromat } = useExperience()

  const data = useMemo(() => {
    const instructions = isLaundromat
      ? LAUNDROMAT_INSTRUCTIONS
      : ALL_INSTRUCTIONS

    instructions[instructions.indexOf(REPLACEABLE)] =
      building?.hasSoapDispensers ? 'soapBrands' : 'dispenser'

    return instructions
  }, [building?.hasSoapDispensers, isLaundromat])

  return data.map((key, index) => (
    <Instruction
      key={`washing-${index}`}
      method='washing'
      icon={key}
      hasDetails={key === 'clock'}
      isLaundromat={isLaundromat}
    />
  ))
}
