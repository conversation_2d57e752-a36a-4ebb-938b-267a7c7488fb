import { endpoints } from 'services/endpoints'

import { injectEndpoints } from './base'

export const activationApi = injectEndpoints({
  endpoints: (builder) => ({
    activate: builder.mutation({
      query: (data) => ({
        url: endpoints.ACTIVATE_MACHINE,
        method: 'POST',
        body: data,
      }),
    }),
  }),
})

export const { useActivateMutation } = activationApi

export const selectSlug = (state) => state.activation.slug
export const selectIndex = (state) => state.activation.index
export const selectUid = (state) => state.activation.uid
