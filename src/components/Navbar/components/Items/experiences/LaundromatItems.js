import useTranslation from 'hooks/useTranslation'
import { hashes } from 'router/paths'

import { Item, PrivateItem } from '../shared/Item'

export const LaundromatItems = ({ onSelected, onAuthError }) => {
  const { t } = useTranslation()

  return (
    <ul className='flex flex-col justify-between grow'>
      <PrivateItem
        path={`#${hashes.wallet}`}
        name={t('home.menu.wallet')}
        onAuthError={onAuthError}
        onClick={onSelected}
      />
      <PrivateItem
        path={`#${hashes.map}`}
        name={t('home.menu.map')}
        onAuthError={onAuthError}
        onClick={onSelected}
      />
      <Item
        path={`#${hashes.instructions}`}
        name={t('home.menu.instructions')}
        onClick={onSelected}
      />
      <Item
        path={`#${hashes.virtualAssistant}`}
        name={t('home.menu.virtualAssistant')}
        onClick={onSelected}
      />
      <Item
        path={`#${hashes.laundromats}`}
        name={t('home.menu.laundromats')}
        onClick={onSelected}
      />
      <Item
        path={`#${hashes.survey}`}
        name={t('home.menu.survey')}
        onClick={onSelected}
      />
    </ul>
  )
}

export default LaundromatItems
