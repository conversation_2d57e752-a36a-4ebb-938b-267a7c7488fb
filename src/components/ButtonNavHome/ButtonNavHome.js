import { useLocation } from 'react-router-dom'

import { Button } from 'components/common/Button'
import { paths } from 'router/paths'
import { useBuilding } from 'hooks/useBuilding'

export const ButtonNavHome = ({ children, hash, ...props }) => {
  const { pathname } = useLocation()
  const { building } = useBuilding()

  const isHome = pathname === paths.index
  const href = isHome
    ? `#${hash}`
    : paths.indexWithBuilding(building?.slug, hash)

  return (
    <Button type='a' href={href} {...props}>
      {children}
    </Button>
  )
}
