import React from 'react'

import flatten from 'flat'
import ReactDOM from 'react-dom'
import { HelmetProvider } from 'react-helmet-async'
import { IntlProvider } from 'react-intl'
import { Provider } from 'react-redux'
import { PersistGate } from 'redux-persist/integration/react'

import { persistor, store } from 'state/store'
import App from './App'
import { DEFAULT_LANGUAGE } from './constants/constants'
import locales from './locales'

import './index.scss'

import './assets/fonts/RoundedMplus1c-Black.ttf'
import './assets/fonts/RoundedMplus1c-ExtraBold.ttf'
import './assets/fonts/RoundedMplus1c-Medium.ttf'
import './assets/fonts/RoundedMplus1c-Thin.ttf'
import './assets/fonts/RoundedMplus1c-Bold.ttf'
import './assets/fonts/RoundedMplus1c-Light.ttf'
import './assets/fonts/RoundedMplus1c-Regular.ttf'

// Internationalization setup
// const usersLocale = navigator.language.split('-')[0]
// const supportedUserLocale = SUPPORTED_LANGUAGES.includes(usersLocale)
// const locale = supportedUserLocale ? usersLocale : DEFAULT_LANGUAGE
const locale = DEFAULT_LANGUAGE
const messages = locales[locale]

ReactDOM.render(
  <React.StrictMode>
    <IntlProvider
      messages={flatten(messages)}
      locale={locale}
      defaultLocale={DEFAULT_LANGUAGE}
    >
      <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
          <HelmetProvider>
            <App />
          </HelmetProvider>
        </PersistGate>
      </Provider>
    </IntlProvider>
  </React.StrictMode>,
  document.getElementById('root')
)
