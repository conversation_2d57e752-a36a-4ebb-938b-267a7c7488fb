import {
  bool,
  elementType,
  number,
  oneOf,
  oneOfType,
  shape,
  string,
} from 'prop-types'

import {
  apiResultErrorCode,
  apiResultErrorMessage,
} from 'constants/apiResultError'
import { httpStatusCode } from 'constants/httpStatusCode'
import { useTranslation } from 'hooks/useTranslation'

export const ErrorMessage = ({
  apiError: { originalStatus, status, data = {} } = {},
  appError,
  mapping = {},
  defaultError,
  show = true,
  ...props
}) => {
  if (!show) return <></>

  if (typeof status !== 'number') {
    // status === PARSING_ERROR

    const ByStatusCode = mapping[originalStatus]
    if (ByStatusCode) {
      return <ByStatusCode {...props} />
    }
  }

  if (typeof status === 'number') {
    const ByStatusCode = mapping[status]
    if (ByStatusCode) {
      return <ByStatusCode {...props} />
    }
  }

  const { resultCode, resultMessage, resultDetail } = data
  if (resultCode) {
    const ByErrorResultCode = mapping[resultCode]
    if (ByErrorResultCode) {
      return <ByErrorResultCode {...props} />
    }
  }
  if (resultMessage) {
    const ByErrorResultMessage = mapping[resultMessage]
    if (ByErrorResultMessage) {
      return <ByErrorResultMessage {...props} />
    }
  }
  if (resultDetail) {
    const ByErrorResultDetail = mapping[resultDetail]
    if (ByErrorResultDetail) {
      return <ByErrorResultDetail {...props} />
    }
  }

  if (appError) {
    const ByAppError = mapping[appError]
    if (ByAppError) {
      return <ByAppError {...props} />
    }
  }

  if (defaultError) {
    const ByDefaultError = mapping[defaultError]
    if (ByDefaultError) {
      return <ByDefaultError {...props} />
    }
  }

  return <GenericError />
}

ErrorMessage.propTypes = {
  apiError: shape({
    originalStatus: number,
    status: oneOfType([oneOf(['PARSING_ERROR']), number]),
    data: shape({
      resultCode: number,
      resultMessage: string,
      resultDetail: string,
    }),
  }),
  appError: string,
  mapping: shape({
    // output: { [httpStatusCode.NOT_FOUND]: elementType, ... }
    ...Object.values(httpStatusCode).reduce(
      (acc, code) => ({ ...acc, [code]: elementType }),
      {}
    ),
    // output: { [apiResultErrorMessage.CARD_NOT_FOUND]: elementType, ... }
    ...Object.values(apiResultErrorMessage).reduce(
      (acc, code) => ({ ...acc, [code]: elementType }),
      {}
    ),
    // output: { [apiResultErrorCode.CARD_NOT_FOUND]: elementType, ... }
    ...Object.values(apiResultErrorCode).reduce(
      (acc, code) => ({ ...acc, [code]: elementType }),
      {}
    ),
  }),
  defaultError: oneOfType([
    oneOf([
      ...Object.values(httpStatusCode),
      ...Object.values(apiResultErrorMessage),
      ...Object.values(apiResultErrorCode),
    ]),
    string,
  ]),
  show: bool,
}

const GenericError = () => {
  const { t } = useTranslation()

  return (
    <div>
      <p>{t('global.error.title')}</p>
      <p>{t('global.error.subtitle', { br: <br /> })}</p>
    </div>
  )
}
