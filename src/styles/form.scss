@import "./variables.scss";
@import "./utilities.scss";

.lm__form {
  &-group {
    &__label { }
    &__element { }
    &__error { }
  }

  &-input {
    &--disabled { }
    &--error { }

    &__checkbox {
      $checkmark-side: 1.5rem;

      display: block;
      padding-left: $checkmark-side;
      position: relative;

      & span {
        background-color: $color-cornflower;
        height: $checkmark-side;
        position: absolute;
        transform: translateX(-150%) translateY(20%);
        width: $checkmark-side;

        &::after {
          border-width: 0 .2rem .2rem 0;
          content: "";
          display: none;
          height: 1rem;
          left: 35%;
          position: absolute;
          top: 5%;
          transform: rotate(45deg);
          width: .5rem;
        }
      }

      &--checked {
        & span {
          background-color: $color-blue;

          &::after {
            display: block;
          }
        }
      }

      &:hover span {
        background-color: $color-skyblue;
      }
    }
  }
}
