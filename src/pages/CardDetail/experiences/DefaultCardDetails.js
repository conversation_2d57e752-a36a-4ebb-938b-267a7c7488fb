import { Redirect, useParams } from 'react-router-dom'

import { Card } from 'components/Card'
import { Conditional, ShowIf } from 'components/common/Conditional'
import { PageTitle } from 'components/common/PageTitle'
import { CARD } from 'constants/models/card'
import { useCard } from 'hooks/useCard'
import { paths } from 'router/paths'

import { AssistantCta } from '../components/AssistantCta'
import { CardState } from '../components/CardState'
import { Cost } from '../components/Cost'
import { Skeleton } from '../components/Skeleton'

export const DefaultCardDetails = () => {
  const { uid } = useParams()
  const { isFetching, isError, card } = useCard({ uid })

  if (isError) return <Redirect to={paths.cardError} />
  if (isFetching || !card) return <Skeleton />

  return (
    <div className='flex flex-col items-center'>
      <PageTitle
        className='my-10'
        title={card.uid}
        subtitle={<CardState state={card.state} />}
      />

      <Conditional>
        <ShowIf condition={card.contractType === CARD.CONTRACT_TYPE.PREPAID}>
          <Card.Balance className='mb-8' card={card} />
        </ShowIf>
        <ShowIf condition={card.contractType === CARD.CONTRACT_TYPE.POSTPAID}>
          <Cost className='mb-8' rate={card.buildings?.[0].rate} />
        </ShowIf>
      </Conditional>

      <Card.Activity className='mb-8' items={card.activity} />

      <AssistantCta />
    </div>
  )
}

export default DefaultCardDetails
