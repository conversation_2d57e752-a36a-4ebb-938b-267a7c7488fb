import { useEffect } from 'react'

import { Logo } from 'components/common/Logo'
import { SectionTitle } from 'components/common/SectionTitle'
import { useTranslation } from 'hooks/useTranslation'

import { ERROR, useStateMap } from '../../hooks/useStateMap'

export const LocationTypeFilters = ({ onSelect, locationTypes = [] }) => {
  const { t } = useTranslation()
  const { report } = useStateMap()

  useEffect(() => {
    if (locationTypes?.length === 1) onSelect?.(locationTypes[0])
    if (!locationTypes?.length) report(ERROR.EMPTY)
  }, [onSelect, locationTypes, report])

  return (
    <>
      <SectionTitle
        className='mb-20'
        intlId='mapByBuilding.filters.locationType.title'
      />

      {locationTypes.map((type) => (
        <Location
          key={type}
          title={t(`mapByBuilding.filters.locationType.${type}`)}
          onClick={() => onSelect(type)}
        />
      ))}
    </>
  )
}

const Location = ({ title = '', onClick }) => (
  <button
    onClick={onClick}
    className='flex flex-col items-center justify-center mb-10'
  >
    <Logo className='w-32 h-32 mb-4' />
    <h3 className='uppercase lm__font--40'>{title}</h3>
  </button>
)
