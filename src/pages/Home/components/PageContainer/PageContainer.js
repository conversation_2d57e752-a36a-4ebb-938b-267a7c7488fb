import { useEffect } from 'react'
import { useLocation, useParams } from 'react-router-dom'

import { useBuilding } from 'hooks/useBuilding'

import { Welcome } from '../Welcome'

export const PageContainer = ({ children }) => {
  // store building slug and keep url unchanged
  const { slug } = useParams()
  useBuilding({ slug })

  // scroll to section if hash is present
  const { hash } = useLocation()
  useEffect(() => {
    if (typeof window === 'undefined') return

    if (hash) {
      const section = document.getElementById(hash.slice(1))
      section?.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }, [hash])

  return (
    <>
      <Welcome />
      {children}
    </>
  )
}
