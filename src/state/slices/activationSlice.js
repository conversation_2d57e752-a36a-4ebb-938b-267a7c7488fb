import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  slug: undefined,
  index: undefined,
  uid: undefined,
}

const activationSlice = createSlice({
  name: 'activation',
  initialState,
  reducers: {
    setSlug: (state, action) => {
      state.slug = action.payload
    },
    setIndex: (state, action) => {
      state.index = action.payload
    },
    setUid: (state, action) => {
      state.uid = action.payload
    },
  },
  extraReducers: (builder) => {
    // builder.addMatcher(logoutFulfilled, () => initialState)
    // TODO: think about this case, if i load another building?
    //        when load machines
  },
})

export const { setSlug, setIndex, setUid } = activationSlice.actions

export default activationSlice.reducer
