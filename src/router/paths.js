export const hashes = {
  card: 'card',
  end: 'end',
  instructions: 'instructions',
  laundromats: 'laundromats',
  map: 'map',
  survey: 'survey',
  virtualAssistant: 'virtual-assistant',
  wallet: 'wallet',
}

export const paths = {
  activation: (
    buildingSlug = ':slug',
    machineIndex = ':index',
    card = ':uid'
  ) => `/map/${buildingSlug}/activation/${machineIndex}/${card}`,
  cardDetail: (uid = ':uid') => `/card/${uid}`,
  cardError: '/card/error',
  cardRegistration: '/card-registration',
  forgotPassword: '/forgot-password',
  index: '/',
  indexWithBuilding: (slug = ':slug', hash = '') =>
    `/${slug}${hash ? `#${hash}` : ''}`,
  login: '/login',
  mapByBuilding: (buildingSlug = ':slug') => `/map/${buildingSlug}`,
  singUp: '/sing-up',
}
