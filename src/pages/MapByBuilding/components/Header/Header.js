import { useEffect, useMemo } from 'react'

import { Dropdown } from 'components/common/Dropdown'
import { SkeletonLoader } from 'components/common/SkeletonLoader'
import { ACTIVATION_FEATURE_ENABLED } from 'constants/configurations'
import { useActivation } from 'hooks/useActivation'
import { useNavigation } from 'hooks/useNavigation'
import { useTranslation } from 'hooks/useTranslation'
import { useUserCards } from 'hooks/useUserCards'
import { paths } from 'router/paths'

import { useMapBuilding } from '../../hooks/useMapBuilding'

export const Header = () => {
  const { t } = useTranslation()
  const { building, isFetching: isFetchingBuilding } = useMapBuilding()
  const { isFetching: isFetchingCards } = useUserCards()

  if (isFetchingBuilding || isFetchingCards) {
    return <Skeleton />
  }

  return (
    <>
      <BuildingSelector />
      <div className='px-6'>
        <p className='text-center font-light lm__font--40'>
          {building
            ? t('mapByBuilding.subtitle', {
                activation: ACTIVATION_FEATURE_ENABLED ? 'enabled' : 'disabled',
              })
            : t('mapByBuilding.unselectedBuilding')}
        </p>
      </div>
    </>
  )
}

const BuildingSelector = () => {
  const { building, isFetching, updateSlug, buildingCards, laundromatCards } =
    useMapBuilding()
  const { updateSelection } = useActivation()

  const { goTo } = useNavigation()
  const { t } = useTranslation()

  const data = useMemo(() => {
    const bCards = buildingCards.map((card) => ({
      id: card.uid,
      name: `${card.buildings?.[0]?.name} - ${card.unit?.number}`,
      building: card.buildings?.[0]?.slug,
      uid: card.uid,
    }))

    const lCards = laundromatCards.reduce((acc, card) => {
      card.buildings?.forEach((building, index) => {
        acc.push({
          id: `${card.uid}.${index}`,
          name: building.name,
          building: building.slug,
          uid: card.uid,
        })
      })

      return acc
    }, [])

    return [...bCards, ...lCards]
  }, [buildingCards, laundromatCards])

  const selected = useMemo(() => {
    const found = data.find((item) => item.building === building?.slug)
    if (found) return found
    if (data?.length === 1) return data[0]
  }, [data, building])

  const handleBuildingChange = (option) => {
    if (!option.building || option.building === building?.slug) return

    goTo(paths.mapByBuilding(option.building))
  }

  useEffect(() => {
    if (selected) {
      updateSlug(selected.building)
      updateSelection({
        uid: selected.uid,
        slug: selected.building,
      })
    }
  }, [selected, updateSelection, updateSlug])

  if (isFetching) return null

  return (
    <Dropdown
      className='w-4/5 mt-10 mb-2'
      placeholder={t('mapByBuilding.select.placeholder')}
      data={data}
      selected={selected}
      deactivateOnSingleOption
      onSelected={handleBuildingChange}
    />
  )
}

const Skeleton = () => (
  <div className='flex flex-col items-center w-full'>
    <div className='w-4/5 mt-10 mb-2'>
      <SkeletonLoader className='h-20' />
    </div>
    <div className='w-full px-6'>
      <SkeletonLoader className='h-16' />
    </div>
  </div>
)
