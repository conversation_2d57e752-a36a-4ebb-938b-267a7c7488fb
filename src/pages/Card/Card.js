import { Layout } from 'components/common/Layout'
import { SectionTitle } from 'components/common/SectionTitle'
import { hashes } from 'router/paths'

import { CardQuery } from './components/CardQuery'
import { CardRequest } from './components/CardRequest'

export const Card = () => (
  <Layout.Section
    id={hashes.card}
    className='relative flex flex-col items-center h-screen'
  >
    <SectionTitle className='my-20' intlId='card.title' />

    <CardQuery className='mb-16' />
    <CardRequest />
  </Layout.Section>
)
