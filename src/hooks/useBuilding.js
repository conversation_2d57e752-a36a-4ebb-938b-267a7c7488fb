import { useCallback, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import { selectSlug, useGetBuildingQuery } from 'services/building'
import { setSlug } from 'state/slices/buildingSlice'

export const useBuilding = ({ slug, level } = {}) => {
  const dispatch = useDispatch()
  const buildingSlug = useSelector(selectSlug)
  const { data: building, ...rest } = useGetBuildingQuery(
    { slug: buildingSlug, level },
    {
      skip: !buildingSlug,
    }
  )

  const updateSlug = useCallback(
    (_slug) => {
      if (_slug) dispatch(setSlug(_slug))
    },
    [dispatch]
  )

  useEffect(() => {
    updateSlug(slug)
  }, [slug, updateSlug])

  return {
    building,
    ...rest,
    slug: buildingSlug,
    updateSlug,
  }
}
