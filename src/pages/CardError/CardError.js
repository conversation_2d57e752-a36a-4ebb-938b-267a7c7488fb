import { ErrorMessage } from 'components/ErrorMessage'
import { httpStatusCode } from 'constants/httpStatusCode'
import { useCard } from 'hooks/useCard'

import { NotFoundError } from './components/NotFoundError'

export const CardError = () => {
  const { error } = useCard()

  return (
    <ErrorMessage
      apiError={error}
      defaultError={httpStatusCode.NOT_FOUND}
      mapping={{
        [httpStatusCode.NOT_FOUND]: NotFoundError,
      }}
    />
  )
}
