import cn from 'classnames'
import {
  arrayOf,
  bool,
  elementType,
  func,
  oneOf,
  shape,
  string,
} from 'prop-types'
import { useMemo, useState } from 'react'
import { useLongPress } from 'use-long-press'

import Icon from 'components/common/Icon'

export const Dropdown = ({
  data = [],
  selected,
  className = '',
  placeholder = '',
  onSelected,
  deactivateOnSingleOption = false,
  deactivateHighlighting = true,
  OptionTemplate = Name,
}) => {
  const [show, setShow] = useState(false)
  const [selectedOption, setActiveItem] = useState(selected)

  const [highlightedOption, setHighlightedOption] = useState()

  const options = useMemo(
    () => data.filter(({ id }) => id !== selectedOption?.id),
    [data, selectedOption]
  )
  const deactivatedBySingleOption =
    options.length <= 0 && deactivateOnSingleOption

  const handleToggle = () => {
    if (deactivatedBySingleOption) return

    setShow(!show)
  }

  const handleItemClick = (item) => {
    if (deactivatedBySingleOption) return

    setShow(false)
    onSelected?.(item)

    if (item.disabled) return

    setActiveItem(item)
  }

  const optionClasses = [
    'flex w-full justify-between items-center',
    'font-medium lm__font--50 uppercase text-center',
  ]
  const optionActiveClasses = [
    'text-skyblue',
    {
      'cursor-pointer hover:bg-cornflower': !deactivatedBySingleOption,
      'cursor-not-allowed': deactivatedBySingleOption,
    },
  ]
  const containerClasses = [
    'bg-white',
    'rounded-lg',
    'shadow-md',
    'border-4 border-skyblue',
  ]

  return (
    <div className={`z-40 ${className}`}>
      <div className='relative inline-block text-left w-full'>
        <button
          id='list-button'
          type='button'
          className={cn(
            optionClasses,
            optionActiveClasses,
            containerClasses,
            'p-3'
          )}
          aria-haspopup='listbox'
          aria-expanded='true'
          aria-label={placeholder}
          onClick={handleToggle}
        >
          <OptionTemplate
            name={selectedOption?.name || placeholder}
            selected
            direction='down'
            placeholder={placeholder}
            hideIcon={deactivatedBySingleOption}
            option={selectedOption}
            location={selectedOption ? 'closed' : 'placeholder'}
          />
        </button>

        <div
          className={cn(
            'absolute right-0 top-0 w-full origin-top-right',
            containerClasses,
            'focus:outline-none',
            {
              'transition-all duration-100 ease-in-out': true,
              'transform opacity-0 invisible': !show,
              'transform opacity-100': show,
            }
          )}
        >
          <ul
            tabIndex='-1'
            role='listbox'
            aria-labelledby='list-button'
            aria-activedescendant={
              selectedOption
                ? `listbox-option-${selectedOption.id}`
                : 'listbox-option-placeholder'
            }
          >
            {selectedOption ? (
              <li
                key={selectedOption.id}
                id={`listbox-option-${selectedOption.id}`}
                role='option'
                aria-selected='true'
                className={cn(optionClasses, optionActiveClasses, 'p-3')}
                onClick={() => handleItemClick(selectedOption)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ')
                    handleItemClick(selectedOption)
                }}
              >
                <OptionTemplate
                  name={selectedOption.name}
                  selected
                  direction='up'
                  option={selectedOption}
                  location='first'
                />
              </li>
            ) : (
              <li
                key={'placeholder'}
                id='listbox-option-placeholder'
                role='option'
                aria-selected='true'
                aria-disabled='true'
                className={cn(
                  'text-silverChalice bg-lightSilver',
                  'cursor-not-allowed',
                  'p-3',
                  optionClasses
                )}
              >
                <OptionTemplate
                  name={placeholder}
                  placeholder={placeholder}
                  selected
                  direction='up'
                  location='placeholder'
                />
              </li>
            )}

            {options.map((option) => {
              const selected = selectedOption?.id === option.id
              const highlighted = highlightedOption?.id === option.id

              return (
                <Option
                  key={option.id}
                  option={option}
                  className={cn(optionClasses, optionActiveClasses, {
                    'p-3': !highlighted,
                    'bg-lightSilver ': highlighted,
                  })}
                  location='list'
                  highlighted={highlighted}
                  selected={selected}
                  onClick={handleItemClick}
                  onHighlightedOption={setHighlightedOption}
                  OptionTemplate={OptionTemplate}
                  deactivateHighlighting={deactivateHighlighting}
                />
              )
            })}
          </ul>
        </div>
      </div>
    </div>
  )
}

const Option = ({
  option,
  selected,
  highlighted,
  onHighlightedOption,
  onClick,
  className,
  OptionTemplate,
  deactivateHighlighting,
}) => {
  const longPressHandlers = useLongPress(
    !deactivateHighlighting ? () => onHighlightedOption?.(option) : null
  )

  return (
    <li
      id={`listbox-option-${option.id}`}
      className={className}
      role='option'
      aria-selected='false'
      onClick={() => onClick?.(option)}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') onClick?.(option)
      }}
      {...longPressHandlers()}
    >
      <OptionTemplate
        name={option.name}
        selected={selected}
        direction='up'
        option={option}
        location='list'
        highlighted={highlighted}
      />
    </li>
  )
}

const Name = ({
  name = '',
  selected = false,
  direction = 'down',
  placeholder = '',
  hideIcon = false,
}) => (
  <>
    {name || placeholder}
    {selected && !hideIcon ? (
      <Icon
        kind='chevron'
        className={cn('w-6 h-6', {
          'rotate-90': direction === 'down',
          '-rotate-90': direction === 'up',
        })}
        color='skyblue'
      />
    ) : null}
  </>
)

const OptionObj = shape({
  id: string,
  name: string,
  disabled: bool,
})
Name.propTypes = {
  name: string,
  selected: bool,
  direction: oneOf(['down', 'up']),
  placeholder: string,
  hideIcon: bool,
  option: OptionObj,
  location: oneOf([
    'closed', // the template is used to render option when it is close
    'placeholder', // the template is used to render placeholder when it is close or disabled open
    'first', // the template is used to render first option when it is open
    'list', // the template is used to render list options when it is open
  ]),
  highlighted: bool,
}
Dropdown.propTypes = {
  data: arrayOf(OptionObj).isRequired,
  selected: OptionObj,
  className: string,
  placeholder: string,
  onSelected: func,
  deactivateOnSingleOption: bool,
  OptionTemplate: elementType,
}
