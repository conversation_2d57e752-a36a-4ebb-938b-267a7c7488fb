import { useIntl } from 'react-intl'

export const useTranslation = () => {
  const intl = useIntl()

  return {
    t: (message, values) => intl.formatMessage({ id: message }, values),
    formatDate: intl.formatDate,
    formatNumber: intl.formatNumber,
    formatTime: intl.formatTime,
    formatTimeToParts: (value, options) =>
      intl.formatTimeToParts(value, options).reduce(
        (acc, part) => ({
          ...acc,
          [part.type]: part.value,
        }),
        {}
      ),
  }
}

export default useTranslation
