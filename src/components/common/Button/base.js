import { oneOf, string } from 'prop-types'

/* Style */

export const classes = {
  BASE: 'disabled:opacity-75 py-2 rounded-2xl text-white font-extrabold uppercase',
  PRIMARY: 'bg-blue',
  SECONDARY: 'bg-skyblue',
  TERTIARY: 'bg-lightSilver',
}

/* Props types */

export const types = {
  BUTTON: 'button', // button
  ANCHOR: 'a', // anchor
  SUBMIT: 'submit', // form submit
}

export const kinds = {
  PRIMARY: 'primary',
  SECONDARY: 'secondary',
  TERTIARY: 'tertiary',
}

export const icons = {
  NEXT: 'next',
  BACK: 'back',
}

export const basePropTypes = {
  className: string,
  type: oneOf(Object.values(types)),
  kind: oneOf(Object.values(kinds)),
}
