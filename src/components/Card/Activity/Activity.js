import Icon from 'components/common/Icon'
import { CARD } from 'constants/models/card'
import useTranslation from 'hooks/useTranslation'

const MAX_ITEMS_COUNT = 8
export const Activity = ({ items, className = '' }) => {
  const { t } = useTranslation()

  return (
    <div className={`flex flex-col px-4 ${className}`}>
      <h2 className='lm__font--50 text-skyblue font-extrabold text-center mb-4'>
        {t('global.card.activity.title')}
      </h2>

      <div className='bg-cornflower rounded-2xl py-2 px-4 m-auto'>
        <Table items={items} />
      </div>
    </div>
  )
}

const Table = ({ items = [] }) => {
  const { t, formatDate, formatNumber } = useTranslation()

  if (items.length === 0) {
    return (
      <div className='flex items-center px-4 h-40'>
        <p
          className='lm__font--30 font-black text-center'
          aria-live='assertive'
        >
          {t('global.card.activity.table.empty')}
        </p>
      </div>
    )
  }

  return (
    <table className='table-fixed'>
      <tbody>
        {items
          ?.slice(0, MAX_ITEMS_COUNT)
          .map(({ type, timestamp, amount }, index) => (
            <tr key={index}>
              <Cell>
                <Icon
                  kind={
                    {
                      [CARD.ACTIVITY_TYPE.WASH]: 'machine',
                      [CARD.ACTIVITY_TYPE.DRY]: 'wind',
                      [CARD.ACTIVITY_TYPE.CREDIT]: 'coin',
                    }[type]
                  }
                  className='w-5'
                  color='blue'
                  srText={type}
                />
              </Cell>
              <Cell className='text-white'>
                {formatDate(timestamp, {
                  day: '2-digit',
                  month: '2-digit',
                  year: '2-digit',
                })}
              </Cell>
              <Cell>
                {t('global.currency.short')}
                {formatNumber(Math.abs(amount || 0))}
              </Cell>
              <Cell className='text-white'>{type}</Cell>
            </tr>
          ))}
      </tbody>
    </table>
  )
}

const Cell = ({ children, className = '' }) => (
  <td
    className={`w-1/4 lm__font--30 font-black text-center py-2 px-3 ${className}`}
  >
    {children}
  </td>
)
