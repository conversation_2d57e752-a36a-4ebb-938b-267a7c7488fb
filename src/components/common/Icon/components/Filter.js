export const Filter = (props) => (
  <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 153 199' {...props}>
    <path
      d='M146.2 93.2879C142.3 84.0879 136.9 76.0879 130 69.1879C123.1 62.2879 115.1 56.8879 105.9 52.9879C96.7 49.0879 86.9 47.0879 76.6 47.0879C66.2 47.0879 56.4 49.0879 47.3 52.9879C38.1 56.8879 30.1 62.2879 23.3 69.1879C16.5 76.0879 11 84.0879 7 93.2879C3 102.488 1 112.288 1 122.588C1 133.088 3 142.988 7 152.088C11 161.288 16.4 169.288 23.2 176.088C30 182.888 38 188.288 47.2 192.188C56.4 196.088 66.2 198.088 76.5 198.088C86.9 198.088 96.7 196.088 105.8 192.188C115 188.288 123 182.888 129.9 176.088C136.8 169.288 142.2 161.288 146.1 152.088C150 142.888 152 133.088 152 122.588C152 112.288 150.1 102.488 146.2 93.2879ZM32 77.9879C44.2 65.7879 59.1 59.6879 76.6 59.6879C94.1 59.6879 109 65.7879 121.2 77.9879C131.4 88.1879 137.4 100.388 139 114.388C139.1 115.388 138.3 116.288 137.3 116.288C137.3 116.288 31.6 116.288 15.9 116.288C14.9 116.288 14 115.388 14.2 114.388C15.8 100.388 21.7 88.2879 32 77.9879ZM121.1 167.188C108.9 179.388 94 185.488 76.5 185.488C59 185.488 44.1 179.388 31.9 167.188C21.7 156.988 15.7 144.788 14.1 130.788C14 129.788 14.8 128.888 15.8 128.888C30.2 128.888 122.8 128.888 137.2 128.888C138.2 128.888 139 129.788 138.9 130.788C137.3 144.888 131.4 156.988 121.1 167.188Z'
      stroke='white'
      strokeWidth='1.5063'
      strokeMiterlimit='10'
    />
    <path
      d='M76.5 118.688L76.9 26.7876'
      strokeWidth='12.0505'
      strokeMiterlimit='10'
      strokeLinecap='round'
    />
    <path
      d='M57.5 24.5877C58.2 24.0877 66.8 14.8877 72.4 8.98768C74.9 6.28768 79.2 6.38768 81.6 8.98768L95.9 24.4877'
      strokeWidth='12.0505'
      strokeMiterlimit='10'
      strokeLinecap='round'
      fill='none'
    />
  </svg>
)

Filter.toString = () => 'filter'
