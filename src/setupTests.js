import { Suspense } from 'react'

import { configureStore } from '@reduxjs/toolkit'
import '@testing-library/jest-dom'
import { render as rtlRender } from '@testing-library/react'
import flatten from 'flat'
import { IntlProvider } from 'react-intl'
import { Provider } from 'react-redux'
import { BrowserRouter } from 'react-router-dom'

import { RecaptchaProvider } from 'hooks/useRecaptcha'
import 'services/auth'
import { api } from 'services/base'
import locales from './locales'
import auth from './state/slices/authSlice'
import building from './state/slices/buildingSlice'
import card from './state/slices/cardSlice'
import { ExperienceProvider } from 'lib/experience'

const reducer = {
  // TODO: Update when we have reducers
  [api.reducerPath]: api.reducer,
  auth,
  building,
  card,
}

const render = (
  ui,
  {
    locale = 'es',
    preloadedState,
    store = configureStore({ reducer, preloadedState }),
    ...renderOptions
  } = {}
) => {
  const Wrapper = ({ children }) => (
    <IntlProvider locale={locale} messages={flatten(locales[locale])}>
      <Suspense fallback={<div>Loading...</div>}>
        <Provider store={store}>
          <RecaptchaProvider>
            <BrowserRouter>
              <ExperienceProvider>{children}</ExperienceProvider>
            </BrowserRouter>
          </RecaptchaProvider>
        </Provider>
      </Suspense>
    </IntlProvider>
  )

  return rtlRender(ui, { wrapper: Wrapper, ...renderOptions })
}

beforeEach(() => {
  // Mock scrollIntoView since jsdom doesn't implement it
  // This is needed because <Home /> uses scrollIntoView for smooth scrolling to sections
  Element.prototype.scrollIntoView = jest.fn()

  // Mock fetch globally
  global.fetch = jest.fn()
  jest.spyOn(window, 'fetch').mockImplementation((...args) => {
    // eslint-disable-next-line no-console
    console.warn('window.fetch is not mocked for this call', ...args)
    return Promise.reject(new Error('This must be mocked!'))
  })
})

afterEach(() => {
  window.fetch.mockRestore()
})

// re-export everything
export * from '@testing-library/react'

// override render method
export { render }
