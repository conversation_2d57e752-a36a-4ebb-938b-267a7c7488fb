import { FormattedMessage } from 'react-intl'
import { Link, useLocation } from 'react-router-dom'

import useAuth from 'hooks/useAuth'
import { paths } from 'router/paths'

import { Items } from '../Items'

export const Menu = ({ onSelected, className }) => (
  <div className={`absolute w-screen h-screen lm__background ${className}`}>
    <div className='flex flex-col justify-end text-center h-full'>
      <Items onSelected={onSelected} />

      <LogInLink onClick={onSelected} />
    </div>
  </div>
)

const LogInLink = ({ onClick }) => {
  const { authenticated } = useAuth()
  const location = useLocation()

  if (authenticated) return null

  return (
    <Link
      to={{
        pathname: paths.login,
        state: { from: location.pathname },
      }}
      className='lm__font--60 font-light mb-12'
      onClick={onClick}
    >
      <FormattedMessage
        id='home.menu.login'
        values={{
          a: (chunks) => <span className='font-bold'>{chunks}</span>,
        }}
      />
    </Link>
  )
}
