import { DEFAULT_QUERY_LEVEL } from 'constants/constants'
import { buildEndpoint, endpoints } from 'services/endpoints'

import { injectEndpoints } from './base'

export const machineApi = injectEndpoints({
  endpoints: (builder) => ({
    getMachine: builder.query({
      query: ({ slug, level = DEFAULT_QUERY_LEVEL }) => ({
        url: buildEndpoint(endpoints.GET_MACHINE, { slug }, { level }),
        method: 'GET',
      }),
    }),
  }),
})

export const { useGetMachineQuery } = machineApi
