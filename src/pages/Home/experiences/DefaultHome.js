import { lazy } from 'react'

import { useAuth } from 'hooks/useAuth'
import { Card } from 'pages/Card'
import { End } from 'pages/End'
import { Instructions } from 'pages/Instructions'
import { Laundromats } from 'pages/Laundromats'
import { Survey } from 'pages/Survey'
import { VirtualAssistant } from 'pages/VirtualAssistant'
import { PageContainer } from '../components/PageContainer'

const Map = lazy(() =>
  import(/* webpackChunkName: "logged-in-exp" */ 'pages/Map')
)

export const DefaultHome = () => {
  const { authenticated } = useAuth()

  return (
    <PageContainer>
      <Card />
      {!!authenticated && <Map />}
      <Instructions />
      <VirtualAssistant />
      <Laundromats />
      <Survey />
      <End />
    </PageContainer>
  )
}

export default DefaultHome
