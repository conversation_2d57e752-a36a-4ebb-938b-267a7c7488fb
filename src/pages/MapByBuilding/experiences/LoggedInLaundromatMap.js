import { Prices } from 'components/Prices'
import { MACHINE } from 'constants/models/machine'
import { useTranslation } from 'hooks/useTranslation'

import { BuildingSelected } from '../components/BuildingSelected'
import { Container } from '../components/Container'
import { Filters } from '../components/Filters'
import { Machines } from '../components/Machines'
import {
  MACHINES_GROUPED_RESERVED_KEYS,
  useMapBuilding,
} from '../hooks/useMapBuilding'
import { STEP, useStateMap } from '../hooks/useStateMap'

export const LoggedInLaundromatMap = () => {
  const { t } = useTranslation()
  const { step, type, capacity, advance } = useStateMap()

  const { machinesGrouped } = useMapBuilding()
  const types = [MACHINE.TYPE.WASHER, MACHINE.TYPE.DRYER]
  const capacities = Object.keys(machinesGrouped?.type?.[type] ?? {})
    .filter((x) => !MACHINES_GROUPED_RESERVED_KEYS.includes(x))
    .sort()

  return (
    <Container className='justify-around'>
      <BuildingSelected />

      <Filters
        mode={step}
        types={types}
        capacities={capacities}
        onSelect={advance}
      />

      {step === STEP.MAP ? (
        <>
          <p className='text-center px-14'>
            {t('mapByBuilding.experience.loggedInLaundromat.subtitle')}
          </p>
          <Machines filter={{ type, capacity }} />
        </>
      ) : (
        <></>
      )}

      <Prices className='w-full px-4' filter={{ capacity }} />
    </Container>
  )
}

export default LoggedInLaundromatMap
