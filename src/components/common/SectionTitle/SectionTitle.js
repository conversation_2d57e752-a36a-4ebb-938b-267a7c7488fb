import { FormattedMessage } from 'react-intl'

export const SectionTitle = ({ className = '', intlId, values = {} }) => (
  <h2 className={`lm__title--sub font-light	text-center ${className}`}>
    <FormattedMessage
      id={intlId}
      values={{
        t: (chunks) => (
          <span className='text-skyblue font-extrabold uppercase lm__title--page'>
            {chunks}
          </span>
        ),
        br: <br />,
        ...values,
      }}
    />
  </h2>
)
