import { SkeletonLoader } from 'components/common/SkeletonLoader'

export const Skeleton = () => (
  <div className='flex flex-col items-center'>
    {/* title */}
    <SkeletonLoader className='w-[210px] h-[50px] my-10' />

    {/* TODO: is dropdown necessary? */}

    {/* balance title */}
    <SkeletonLoader className='w-[165px] h-[32px] mb-4' />
    {/* balance box */}
    <SkeletonLoader className='w-[220px] h-[100px] mb-8' />
    {/* balance cta */}
    <SkeletonLoader className='w-[275px] h-[60px] mb-8' />
    {/* history title */}
    <SkeletonLoader className='w-[150px] h-[40px] mb-4' />
    {/* history box */}
    <SkeletonLoader className='w-[375px] h-[340px]' />
  </div>
)
