import { Navbar } from 'components/Navbar'
import { ErrorBoundary } from 'components/common/ErrorBoundary'
import { Icon } from 'components/common/Icon'
import useTranslation from 'hooks/useTranslation'

export const Welcome = () => {
  const { t } = useTranslation()

  return (
    <section className='flex flex-col lm__background text-white'>
      <ErrorBoundary noFallback>
        <Navbar />
      </ErrorBoundary>
      <div className='flex grow flex-col justify-center items-center'>
        <h1 className='lm__title--main mb-10'>{t('home.title')}</h1>
        <Icon kind='logo' color='white' className='w-2/5 md:w-1/6' />
      </div>
    </section>
  )
}
