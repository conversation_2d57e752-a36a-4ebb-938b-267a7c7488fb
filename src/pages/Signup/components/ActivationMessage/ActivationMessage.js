import React from 'react'

import useTranslation from 'hooks/useTranslation'
import { But<PERSON> } from 'components/common/Button'
import { paths } from 'router/paths'

export const ActivationMessage = () => {
  const { t } = useTranslation()

  return (
    <div className='flex flex-col justify-center items-center w-4/5 h-full'>
      <h2 className='lm__font--50 text-center'>
        {t('singup.activationMessage.title')}
      </h2>
      <h3 className='lm__font--50 text-center mb-16'>
        {t('singup.activationMessage.subtitle')}
      </h3>

      <Button kind='secondary' type='a' href={paths.index}>
        {t('singup.activationMessage.cta')}
      </Button>
    </div>
  )
}
