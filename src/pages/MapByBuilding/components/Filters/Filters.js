import { STEP } from '../../hooks/useStateMap'
import { LocationTypeFilters } from './LocationTypeFilters'
import { CapacityFilters } from './CapacityFilters'
import { LocationFilters } from './LocationFilters'
import { TypeFilters } from './TypeFilters'

export const Filters = ({ mode, ...props }) => {
  if (mode === STEP.TYPE) return <TypeFilters {...props} />
  if (mode === STEP.CAPACITY) return <CapacityFilters {...props} />
  if (mode === STEP.LOCATION_TYPE) return <LocationTypeFilters {...props} />
  if (mode === STEP.LOCATION) return <LocationFilters {...props} />

  return <></>
}
