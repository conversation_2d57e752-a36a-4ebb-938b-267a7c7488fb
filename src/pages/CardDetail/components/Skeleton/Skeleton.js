import { SkeletonLoader } from 'components/common/SkeletonLoader'

export const Skeleton = ({ section = 'all' }) => (
  <div className='flex flex-col items-center'>
    {section === 'all' && (
      <>
        {/* title */}
        <SkeletonLoader className='w-[210px] h-[50px] mt-8' />
        {/* subtitle */}
        <SkeletonLoader className='w-[130px] h-[40px] mt-1 mb-10' />
      </>
    )}
    {section !== 'all' && (
      <>
        {/* balance title */}
        <SkeletonLoader className='w-[165px] h-[32px] mb-4' />
        {/* balance box */}
        <SkeletonLoader className='w-[220px] h-[100px] mb-8' />
        {/* balance cta */}
        <SkeletonLoader className='w-[275px] h-[60px] mb-8' />
        {/* history title */}
        <SkeletonLoader className='w-[150px] h-[40px] mb-4' />
        {/* history box */}
        <SkeletonLoader className='w-[375px] h-[340px] mb-8' />
      </>
    )}
    {section !== 'second' && (
      <>
        {/* assistant cta */}
        <SkeletonLoader className='w-[235px] h-[60px]' />
      </>
    )}
  </div>
)
