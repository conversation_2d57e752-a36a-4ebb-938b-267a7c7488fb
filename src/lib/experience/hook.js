import { useContext } from 'react'

import { EXPERIENCE, ExperienceContext } from './context'

export const useExperience = () => {
  const context = useContext(ExperienceContext)
  if (!context) {
    throw new Error('useExperience must be used within an ExperienceProvider')
  }

  const { experience, isActivationEnabled } = context
  return {
    isLoggedInLaundromat: experience === EXPERIENCE.loggedIn.laundromat,
    isLoggedInBuilding: experience === EXPERIENCE.loggedIn.building,
    isLoggedInUnknown: experience === EXPERIENCE.loggedIn.unknown,
    isNonLoggedInLaundromat: experience === EXPERIENCE.nonLoggedIn.laundromat,
    isNonLoggedInBuilding: experience === EXPERIENCE.nonLoggedIn.building,
    isNonLoggedInUnknown: experience === EXPERIENCE.nonLoggedIn.unknown,
    isLaundromat:
      experience === EXPERIENCE.loggedIn.laundromat ||
      experience === EXPERIENCE.nonLoggedIn.laundromat,
    isBuilding:
      experience === EXPERIENCE.loggedIn.building ||
      experience === EXPERIENCE.nonLoggedIn.building,
    isUnknown:
      experience === EXPERIENCE.loggedIn.unknown ||
      experience === EXPERIENCE.nonLoggedIn.unknown,
    experience,
    isActivationEnabled,
  }
}
