import { configureStore } from '@reduxjs/toolkit'
import logger from 'redux-logger'
import { persistStore } from 'redux-persist'

import reducer from 'state/reducer'
import { api } from 'services/base'

const middleware = (getDefaultMiddleware) => {
  const _middleware = getDefaultMiddleware({
    serializableCheck: {
      ignoredActions: ['persist/PERSIST'],
    },
  }).concat(api.middleware)

  if (!process.env.NO_REDUX_LOGGER) {
    _middleware.concat(logger)
  }

  return _middleware
}

export const store = configureStore({
  reducer,
  middleware,
  devTools: true,
})

export const persistor = persistStore(store)
