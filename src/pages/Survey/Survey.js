import { FormattedMessage } from 'react-intl'

import { ButtonIcon } from 'components/common/Button'
import { Layout } from 'components/common/Layout'
import { Logo } from 'components/common/Logo'
import { SectionTitle } from 'components/common/SectionTitle'
import { SURVEY_LINKS } from 'constants/configurations'
import { useTranslation } from 'hooks/useTranslation'
import { useExperience } from 'lib/experience/hook'
import { hashes } from 'router/paths'

export const Survey = () => {
  const { t } = useTranslation()

  const { isLaundromat, isUnknown } = useExperience()

  if (isUnknown) return null

  return (
    <Layout.Section
      id={hashes.survey}
      className='relative flex flex-col items-center justify-around h-screen px-2'
    >
      <div className='flex flex-col items-center'>
        <Logo className='w-28 h-28 mb-10' />

        <SectionTitle intlId='survey.title' className='mb-6' />

        <h3 className='lm__font--40 font-light text-center'>
          <FormattedMessage id='survey.subtitle' values={{ br: <br /> }} />
        </h3>
      </div>

      <div className='flex flex-col items-center'>
        <p className='lm__font--50 text-skyblue font-extrabold text-center uppercase mb-10'>
          <FormattedMessage id='survey.explanation' values={{ br: <br /> }} />
        </p>

        <ButtonIcon
          type='a'
          href={isLaundromat ? SURVEY_LINKS.laundromat : SURVEY_LINKS.building}
          target='_blank'
          rel='noopener noreferrer'
          srText={t('survey.cta')}
          className='mb-4'
        />
      </div>
    </Layout.Section>
  )
}
