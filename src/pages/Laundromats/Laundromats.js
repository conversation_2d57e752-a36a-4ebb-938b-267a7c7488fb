import { Layout } from 'components/common/Layout'
import { SectionTitle } from 'components/common/SectionTitle'
import { Wave } from 'components/common/Wave'
import { hashes } from 'router/paths'

import { Stores } from './components/Stores'

export const Laundromats = () => (
  <Layout.Section
    id={hashes.laundromats}
    className='relative flex flex-col items-center justify-center min-h-screen'
  >
    <Wave direction='down' peakCount={2} height={0} />

    <SectionTitle className='mb-20' intlId='laundromats.title' />
    <Stores />
  </Layout.Section>
)
