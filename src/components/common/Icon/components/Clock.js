export const Clock = (props) => (
  <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 168 168' {...props}>
    <path d='M84 168C72.4138 168 61.6075 165.77 51.3581 161.423C41.1088 157.075 32.3077 151.055 24.7321 143.474C17.1565 135.894 11.1406 126.975 6.68435 116.831C2.22812 106.686 0 95.6496 0 83.9443C0 72.3504 2.22812 61.5368 6.68435 51.2807C11.1406 41.0246 17.1565 32.1062 24.7321 24.5255C32.3077 16.9449 41.2202 10.925 51.4695 6.57731C61.7189 2.2296 72.5252 0 84 0C95.5862 0 106.393 2.2296 116.642 6.57731C126.891 10.925 135.804 16.9449 143.379 24.637C150.955 32.3291 156.971 41.2475 161.427 51.3922C165.883 61.5368 168 72.4618 168 84.0557C168 95.7611 165.772 106.686 161.427 116.942C157.082 127.198 151.066 136.005 143.379 143.586C135.692 151.167 126.78 157.075 116.642 161.534C106.393 165.77 95.5862 168 84 168ZM116.308 118.169L117.979 116.496C120.318 114.155 120.207 110.476 117.867 108.247C109.735 100.666 93.0239 84.7246 91.7984 81.9376C90.573 79.1506 91.2414 54.848 91.5756 43.3656C91.687 40.1327 89.1247 37.4572 85.7825 37.4572H83.2202C80.1008 37.4572 77.5385 39.9098 77.4271 43.0312C77.0928 55.5169 76.313 84.7246 77.6499 87.5116C78.9868 90.2986 99.3741 109.808 108.175 118.28C110.515 120.398 114.08 120.398 116.308 118.169ZM84 153.954C103.496 153.954 119.984 147.153 133.576 133.553C147.167 119.952 153.963 103.453 153.963 83.9443C153.963 64.4353 147.167 47.9363 133.576 34.3358C119.984 20.7352 103.496 13.935 84 13.935C64.504 13.935 48.0159 20.7352 34.4244 34.3358C20.8329 47.9363 14.0372 64.4353 14.0372 83.9443C14.0372 103.453 20.8329 119.952 34.4244 133.553C48.0159 147.153 64.504 153.954 84 153.954Z' />
  </svg>
)

Clock.toString = () => 'clock'
