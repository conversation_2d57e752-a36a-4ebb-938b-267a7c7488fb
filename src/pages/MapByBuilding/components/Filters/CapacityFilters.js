import { useEffect } from 'react'

import { Button } from 'components/common/Button'
import { useTranslation } from 'hooks/useTranslation'

import { ERROR, useStateMap } from '../../hooks/useStateMap'

export const CapacityFilters = ({ onSelect, capacities }) => {
  const { t } = useTranslation()
  const { report } = useStateMap()

  useEffect(() => {
    if (capacities?.length === 1) onSelect?.(capacities[0])
    if (!capacities?.length) report(ERROR.EMPTY)
  }, [onSelect, capacities, report])

  if (!capacities?.length) return null

  return (
    <div className='flex flex-col items-center'>
      <h3 className='lm__font--50 font-light mb-8'>
        {t('mapByBuilding.filters.title')}
      </h3>

      {capacities.map((capacity) => (
        <Button
          key={capacity}
          className='mb-8 w-full'
          kind='secondary'
          onClick={() => onSelect?.(capacity)}
        >
          {t('mapByBuilding.filters.capacity', { capacity })}
        </Button>
      ))}
    </div>
  )
}
