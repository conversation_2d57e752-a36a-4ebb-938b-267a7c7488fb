import { Button } from 'components/common/Button'
import { getPublicSiteData } from 'constants/configurations'
import useTranslation from 'hooks/useTranslation'

const { purchaseCreditsUrl: PURCHASE_CREDITS_URL } = getPublicSiteData()
export const Balance = ({
  card: { balance = '--', uid = '' } = {},
  className = '',
}) => {
  const { t } = useTranslation()

  return (
    <div className={`flex flex-col justify-center items-center ${className}`}>
      <h2 className='lm__title--sub font-light text-center mb-4'>
        {t('global.card.balance.title')}
      </h2>
      <span className='text-white lm__font--80 font-extrabold bg-skyblue rounded-2xl py-4 px-8 text-center mb-8'>
        {t('global.card.balance.value', { balance })}
      </span>

      <Button type='a' href={`${PURCHASE_CREDITS_URL}${uid}`}>
        {t('global.card.balance.cta')}
      </Button>
    </div>
  )
}
