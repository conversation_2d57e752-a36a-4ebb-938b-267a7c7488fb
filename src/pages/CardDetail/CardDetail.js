import { lazy } from 'react'

import { useExperience } from 'lib/experience'

const LoggedInBuildingCardDetails = lazy(() =>
  import(
    /* webpackChunkName: "lbd-exp" */ './experiences/LoggedInBuildingCardDetails'
  )
)

const DefaultCardDetails = lazy(() =>
  import('./experiences/DefaultCardDetails')
)

export const CardDetail = () => {
  const { isLoggedInBuilding } = useExperience()

  if (isLoggedInBuilding) return <LoggedInBuildingCardDetails />
  return <DefaultCardDetails />
}
