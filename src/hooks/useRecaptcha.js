import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react'

import { RECAPTCHA_KEY } from 'constants/configurations'

import useAuth from './useAuth'

const SCRIPT_ID = '__lm_recaptcha_script'

const RecaptchaContext = createContext(null)

export const RecaptchaProvider = ({ children }) => {
  const [isReady, setReady] = useState(false)
  const { authenticated } = useAuth()
  const isSkippable = !!authenticated
  const [tokenState, setTokenState] = useState({
    token: null,
    isFetching: false,
    isError: false,
  })
  const tokenPromiseRef = useRef(null)

  const scriptHandlers = useCallback(() => {
    const attachScript = () => {
      if (isSkippable || document?.getElementById(SCRIPT_ID)) {
        setReady(true)
        return
      }

      const script = document.createElement('script')
      script.id = SCRIPT_ID
      script.src = `https://www.google.com/recaptcha/api.js?render=${RECAPTCHA_KEY}`
      script.async = true
      script.defer = true
      script.onload = () => {
        window.grecaptcha?.ready(() => {
          setReady(true)
        })
      }
      document.body.appendChild(script)
    }

    const detachScript = () => {
      const script = document.getElementById(SCRIPT_ID)
      if (script) document.body.removeChild(script)

      setReady(false)
    }

    return { attachScript, detachScript }
  }, [isSkippable])

  const getToken = useCallback(() => {
    if (isSkippable) return Promise.resolve(null)

    if (!isReady || !window.grecaptcha?.execute) {
      console.error('reCAPTCHA not ready')
      setTokenState({
        token: null,
        isFetching: false,
        isError: true,
      })
      return Promise.resolve(null)
    }

    if (tokenPromiseRef.current) {
      return tokenPromiseRef.current
    }

    setTokenState((prev) => ({ ...prev, isFetching: true, isError: false }))

    tokenPromiseRef.current = window.grecaptcha
      .execute(RECAPTCHA_KEY, { action: 'submit' })
      .then((token) => {
        setTokenState({ token, isFetching: false, isError: false })
        tokenPromiseRef.current = null
      })
      .catch((error) => {
        console.error('Error getting reCAPTCHA token:', error.message)
        setTokenState({ token: null, isFetching: false, isError: true })
        tokenPromiseRef.current = null
      })

    return tokenPromiseRef.current
  }, [isReady, isSkippable])

  return (
    <RecaptchaContext.Provider
      value={{ isReady, getToken, isSkippable, scriptHandlers, ...tokenState }}
    >
      {children}
    </RecaptchaContext.Provider>
  )
}

export const useRecaptcha = () => {
  const context = useContext(RecaptchaContext)
  if (context === null) {
    throw new Error('useRecaptcha must be used within a RecaptchaProvider')
  }

  const {
    scriptHandlers,
    isSkippable,
    getToken,
    isReady,
    token,
    isFetching,
    isError,
  } = context

  useEffect(() => {
    const { attachScript, detachScript } = scriptHandlers()
    attachScript()

    return detachScript
  }, [scriptHandlers])

  return { isSkippable, getToken, isReady, token, isFetching, isError }
}
