import { createContext, useContext, useState } from 'react'

const ActivationContext = createContext(null)
export const PHASE = {
  main: 'main',
  processing: 'processing',
  success: 'success',
  done: 'done',
  error: 'error',
}

export const StateMachineProvider = ({ children }) => {
  const [phase, setPhase] = useState(PHASE.main)
  const [error, setError] = useState()
  const [data, setData] = useState({})

  const next = (event) => {
    if (phase === PHASE.main) setPhase(PHASE.processing)
    else if (phase === PHASE.processing) setPhase(PHASE.success)
    else if (phase === PHASE.success) setPhase(PHASE.done)

    setData((prev) => ({ ...prev, ...event }))
  }

  const reportError = (reportedError) => {
    setPhase(PHASE.error)
    setError(reportedError)
  }

  return (
    <ActivationContext.Provider
      value={{ phase, next, error, reportError, data }}
    >
      {children}
    </ActivationContext.Provider>
  )
}

export const useStateMachine = () => {
  const context = useContext(ActivationContext)
  if (context === null) {
    throw new Error(
      'useStateMachine must be used within a StateMachineProvider'
    )
  }

  return context
}
