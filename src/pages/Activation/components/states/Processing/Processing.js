import { DotLottieReact } from '@lottiefiles/dotlottie-react'

import { Wave } from 'components/common/Wave'
import { useActivation } from 'hooks/useActivation'
import { useTranslation } from 'hooks/useTranslation'

import { useEffect } from 'react'
import { useStateMachine } from '../../../contexts/StateMachineContext'

export const Processing = () => {
  const { t } = useTranslation()
  const { reportError, next } = useStateMachine()
  const {
    trigger,
    isUninitialized,
    isSuccess,
    isError,
    error: apiError,
    data,
  } = useActivation()

  useEffect(() => {
    if (isUninitialized) trigger()
    if (isError) reportError({ apiError })
    if (isSuccess) next(data)
  }, [
    isError,
    isSuccess,
    reportError,
    next,
    data,
    isUninitialized,
    trigger,
    apiError,
  ])

  return (
    <div className='relative flex flex-col items-center justify-center text-center h-screen'>
      <h1 className='lm__title--page font-extrabold text-skyblue'>
        {t('activation.processing.title')}
      </h1>

      <DotLottieReact
        className='w-96 h-96'
        src='/logo-loading.lottie'
        loop
        autoplay
        autoResizeCanvas={false}
      />

      <Wave direction='up' peakCount={3} />
      <Wave direction='up' peakCount={2} color='skyblue' />
    </div>
  )
}
