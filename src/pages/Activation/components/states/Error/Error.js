import { ErrorMessage } from 'components/ErrorMessage'

import { useStateMachine } from '../../../contexts/StateMachineContext'
import { GenericError } from './GenericError'
import { InactiveCardError } from './InactiveCardError'
import { InsufficientCardBalanceError } from './InsufficientCardBalanceError'
import { InvalidUseTimeError } from './InvalidUseTimeError'
import { NotAvailableMachineError } from './NotAvailableMachineError'

export const APP_ERROR = {
  generic: 'generic',
  notAvailableMachine: 'notAvailableMachine',
}

export const API_ERROR = {
  buildingNotFound: 'BUILDING_NOT_FOUND',
  machineNotFound: 'MACHINE_NOT_FOUND',
  cardNotFound: 'CARD_NOT_FOUND',
  inactiveCard: 'INACTIVE_CARD',
  machineActivationFailed: 'MACHINE_ACTIVATION_FAILED',
  insufficientCardBalance: 'INSUFFICIENT_CARD_BALANCE',
  notValidResultType: 'NOT_VALID_RESULT_TYPE',
  invalidUseTime: 'INVALID_USE_TIME',
}

export const Error = () => {
  const { error } = useStateMachine()
  const { appError, apiError } = error

  return (
    <ErrorMessage
      mapping={{
        [APP_ERROR.notAvailableMachine]: NotAvailableMachineError,
        [APP_ERROR.generic]: GenericError,
        [API_ERROR.insufficientCardBalance]: InsufficientCardBalanceError,
        [API_ERROR.inactiveCard]: InactiveCardError,
        [API_ERROR.invalidUseTime]: InvalidUseTimeError,
      }}
      apiError={apiError}
      appError={appError}
      defaultError={APP_ERROR.generic}
    />
  )
}
