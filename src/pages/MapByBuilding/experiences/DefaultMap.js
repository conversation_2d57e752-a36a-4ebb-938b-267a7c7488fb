import { Prices } from 'components/Prices'
import { MACHINE } from 'constants/models/machine'

import { Container } from '../components/Container'
import { Header } from '../components/Header'
import { Machines } from '../components/Machines'

export const DefaultMap = ({ location }) => {
  return (
    <Container>
      <Header location={location} />

      <Machines filter={{ type: MACHINE.TYPE.WASHER }} />
      <Machines filter={{ type: MACHINE.TYPE.DRYER }} />

      <Prices />
    </Container>
  )
}

export default DefaultMap
