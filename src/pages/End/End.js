import Icon from 'components/common/Icon'
import { Layout } from 'components/common/Layout'
import { getPublicSiteData } from 'constants/configurations'

export const End = () => {
  const { text, url } = getPublicSiteData()

  return (
    <Layout.Section className='flex flex-col justify-center items-center text-white lm__background lm__background--end'>
      <a
        className='lm__font--60 flex flex-col items-center'
        href={url}
        style={{ wordBreak: 'break-word' }}
      >
        <Icon kind='logo' color='white' className='w-2/5 md:w-1/6' />
        {text}
      </a>
    </Layout.Section>
  )
}
