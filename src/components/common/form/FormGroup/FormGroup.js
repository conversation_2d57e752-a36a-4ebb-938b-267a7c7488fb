import React from 'react'

export const FormGroup = ({
  children,
  className = '',
  error,
  label,
  name,
  required,
  touched = 'false',
}) => (
  <div
    className={`lm__form-group flex flex-col justify-center w-full ${className}`}
  >
    {label && (
      <label
        htmlFor={name}
        className='lm__form-group__label lm__font--40 font-light text-center mb-1'
      >
        {label}
        {required && <span aria-hidden='true'>*</span>}
      </label>
    )}

    <div className='lm__form-group__element relative'>{children}</div>

    <div className='lm__form-group__error'>
      {error && (
        <p
          id={`${name}-help-text`}
          className='lm__font--30 font-light text-left text-blood first-letter:capitalize mt-1 ml-2'
          aria-live='assertive'
        >
          {error}
        </p>
      )}
    </div>
  </div>
)
