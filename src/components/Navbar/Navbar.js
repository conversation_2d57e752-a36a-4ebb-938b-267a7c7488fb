import { useState } from 'react'

import { Icon } from 'components/common/Icon'
import useTranslation from 'hooks/useTranslation'
import { Menu } from './components/Menu'

export const Navbar = () => {
  const [isMenuVisible, setIsMenuVisible] = useState(false)
  const toggleMenu = () => {
    window.location.hash = ''
    setIsMenuVisible(!isMenuVisible)
  }
  const { t } = useTranslation()

  return (
    <nav className='absolute flex flex-col w-screen'>
      <button onClick={toggleMenu} className='mt-5 mr-8 self-end z-10'>
        <Icon
          kind={isMenuVisible ? 'close' : 'menu'}
          color='white'
          className='w-12 h-12'
          srText={t(`home.menu.icon.${isMenuVisible ? 'close' : 'open'}`)}
        />
      </button>
      {isMenuVisible ? (
        <Menu onSelected={toggleMenu} className='pt-24' />
      ) : null}
    </nav>
  )
}
