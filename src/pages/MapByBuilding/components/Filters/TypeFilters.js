import { useEffect } from 'react'

import { Button } from 'components/common/Button'
import { useTranslation } from 'hooks/useTranslation'

import { ERROR, useStateMap } from '../../hooks/useStateMap'

export const TypeFilters = ({ onSelect, types = [] }) => {
  const { t } = useTranslation()
  const { report } = useStateMap()

  useEffect(() => {
    if (types?.length === 1) onSelect?.(types[0])
    if (!types?.length) report(ERROR.EMPTY)
  }, [onSelect, types, report])

  if (!types?.length) return null

  return (
    <div className='flex flex-col items-center'>
      <h3 className='lm__font--50 font-light mb-8'>
        {t('mapByBuilding.filters.title')}
      </h3>

      {types.map((type, index) => (
        <Button
          key={type}
          className='mb-8 w-full'
          kind={index % 2 === 0 ? 'secondary' : 'primary'}
          onClick={() => onSelect?.(type)}
        >
          {t(`mapByBuilding.filters.type.${type}`)}
        </Button>
      ))}
    </div>
  )
}
