@import "./variables.scss";

.lm {
  &__background {
    background-color: #fff;
    background-image: url("/assets/home_bg.jpg");
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    height: 100vh;
    width: 100vw;

    &--end {
      background-image: url("/assets/end_bg.jpg");
    }
  }

  &__title {
    &--main {
      font-size: $text-font-80;
    }

    &--page {
      font-size: $text-font-70;
      line-height: 2rem;
    }

    &--sub {
      font-size: $text-font-50;
      line-height: 2rem;
    }
  }

  &__font {
    &--30 { font-size: $text-font-30; }
    &--40 { font-size: $text-font-40; }
    &--50 { font-size: $text-font-50; }
    &--60 { font-size: $text-font-60; }
    &--70 { font-size: $text-font-70; }
    &--80 { font-size: $text-font-80; }
    &--100 { font-size: $text-font-100; }
  }
}
