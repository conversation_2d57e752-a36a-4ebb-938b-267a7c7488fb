export const Drop = (props) => (
  <svg viewBox='0 0 130 184' {...props}>
    <g clipPath='url(#clip0_979_507)'>
      <path d='M72.4713 34.4064C67.2414 29.6194 59.1725 29.6194 53.7932 34.4064C18.0805 66.7186 6.96882e-05 94.9918 6.96882e-05 119.076C-0.149356 158.419 29.7357 183.85 62.9081 183.85C95.9311 184 125.966 158.569 126.115 119.376C126.115 95.291 108.333 66.8682 72.4713 34.4064ZM63.3564 173.08C34.5173 173.08 11.8047 150.79 11.8047 119.525C11.8047 101.275 25.5518 77.7885 53.7932 50.1137C59.3219 44.7284 67.9886 44.7284 73.5173 50.1137C101.609 78.0877 115.207 101.424 115.058 119.675C115.058 150.94 92.1955 173.08 63.3564 173.08ZM30.184 120.722C33.1725 120.722 35.4139 122.816 36.0116 125.658C39.1495 143.161 53.9426 149.145 64.7012 148.397C68.138 148.247 70.9771 150.94 70.9771 154.38C70.9771 157.522 68.4369 160.065 65.2989 160.215C48.5633 161.262 28.9886 151.538 24.5058 127.603C23.7587 124.013 26.5978 120.722 30.184 120.722Z' />
      <path
        d='M124.322 5.53497C121.035 2.24392 117 0.598389 112.219 0.598389C107.437 0.598389 103.402 2.24392 100.115 5.53497C96.8278 8.82603 95.1841 12.8651 95.1841 17.652C95.1841 22.439 96.8278 26.4781 100.115 29.7691C103.402 33.0602 107.437 34.7057 112.219 34.7057C117 34.7057 121.035 33.0602 124.322 29.7691C127.609 26.4781 129.253 22.439 129.253 17.652C129.253 12.8651 127.609 8.82603 124.322 5.53497ZM121.334 18.8488C120.736 23.935 116.104 27.3756 111.173 26.7773C106.092 26.1789 102.655 21.5415 103.253 16.6049C103.851 11.5187 108.483 8.07806 113.414 8.67644C118.345 9.27481 121.931 13.7626 121.334 18.8488Z'
        strokeWidth='0.8556'
        strokeMiterlimit='10'
      />
    </g>
    <defs>
      <clipPath id='clip0_979_507'>
        <rect width='130' height='184' fill='white' />
      </clipPath>
    </defs>
  </svg>
)

Drop.toString = () => 'drop'
